@echo off
echo ===================================================
echo.
echo  Starting project build for 'manage'...
echo.
echo ===================================================

echo.
echo Step 1: Cleaning previous build...
call mvnw.cmd clean

echo.
echo Step 2: Packaging the application (skipping tests)...
call mvnw.cmd package -DskipTests

echo.
echo Step 3: Moving the JAR file...

if exist target\\manage-0.0.1-SNAPSHOT.jar (
    if not exist jar mkdir jar
    move target\\manage-0.0.1-SNAPSHOT.jar jar\\
    echo.
    echo ===================================================
    echo.
    echo  Build Successful!
    echo  The JAR file can be found in the 'jar' directory.
    echo.
    echo ===================================================
) else (
    echo.
    echo ===================================================
    echo.
    echo  Build Failed! JAR file not found.
    echo.
    echo ===================================================
)

echo.
pause 