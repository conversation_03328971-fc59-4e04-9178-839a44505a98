@echo off
setlocal

:: =================================================================
:: Configuration
:: =================================================================
:: This is a unique name for our service's process window.
set "SERVICE_WINDOW_TITLE=ManageSrv-8092"
set "JAR_NAME=manage-0.0.1-SNAPSHOT.jar"
set "LOG_DIR=logs"
set "JAR_PATH=..\%JAR_NAME%"

:: =================================================================
:: Script Body
:: =================================================================
cd /d "%~dp0"

echo [INFO] Checking for an already running service instance...
set "PID="
set "RAW_PID="
for /f "tokens=2" %%i in ('tasklist /NH /FI "IMAGENAME eq java.exe" /FI "WINDOWTITLE eq %SERVICE_WINDOW_TITLE%"') do (
    set "RAW_PID=%%i"
)
(echo %RAW_PID%) | findstr /r "^[0-9][0-9]*$" > nul
if %errorlevel% equ 0 (
    set "PID=%RAW_PID%"
)

if defined PID (
    echo [WARNING] Service is already running with PID: %PID%.
    echo Please stop it first using stop.bat, then run this script again.
    pause
    goto :eof
)

if not exist "%JAR_PATH%" (
    echo [ERROR] JAR file not found: %JAR_PATH%
    pause
    goto :eof
)

if not exist "%LOG_DIR%" mkdir "%LOG_DIR%"

echo [INFO] Starting service in the foreground.
echo [INFO] This window will show the application log.
echo [INFO] DO NOT CLOSE THIS WINDOW to keep the service running.
echo =================================================================

:: Start the java process in the foreground, with a specific window title
title %SERVICE_WINDOW_TITLE%
java -jar "%JAR_PATH%"