# Server Configuration
server.port=8111

# Database Configuration
spring.datasource.url=**********************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=Passw0rd12#$
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL57Dialect

# Logging Configuration
logging.file.name=logs/app.log
logging.level.org.springframework=DEBUG
logging.level.com.hj.manage=DEBUG

spring.web.resources.static-locations=classpath:/static/,classpath:/public/,classpath:/resources/,classpath:/META-INF/resources/,file:./uploads/


mysql.dump.path=D:/Application/phpstudy_pro/Extensions/MySQL5.7.26/bin/mysqldump.exe
mysql.username=root
mysql.password=Passw0rd12#$
mysql.database=fixture_manage
mysql.backup.path=D:/QyProjects/gzjiaju/sqlBackups/ 