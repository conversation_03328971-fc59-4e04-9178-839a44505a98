package com.hj.manage.service;

import com.hj.manage.domain.UsageRecord;

import java.time.LocalDateTime;
import java.util.List;

public interface UsageRecordService {
    String registerUsage(String toolCode, String machineCode, String taskCode, LocalDateTime startTime, LocalDateTime endTime);

    long getUsageCountByToolCode(String toolCode);

    long getTotalUsageTimeInMinutes(String toolCode);

    // 获取工具的所有使用记录
    List<UsageRecord> getUsageRecordsByToolCode(String toolCode);
}