package com.hj.manage.service;

import com.hj.manage.domain.User;

import java.util.List;

public interface UserService {

    // 登录
    User login(String username, String password);

    // 添加用户
    User add(User user);

    // 重置密码
    User resetPassword(String username, String phone, String newPassword);

    //  更新用户信息
    User updateUserInfo(Integer userId, User updatedUser);

    //  获取所有用户
    List<User> getAllUsers();

    User registerUser(String username, String password, String phone);
    
    // 删除用户
    void deleteUser(Integer userId);
}