package com.hj.manage.service.impl;

import com.hj.manage.domain.Tool;
import com.hj.manage.domain.UsageRecord;
import com.hj.manage.repository.UsageRecordRepository;
import com.hj.manage.service.ToolService;
import com.hj.manage.service.UsageRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
public class UsageRecordServiceImpl implements UsageRecordService {

    private final UsageRecordRepository usageRecordRepository;

    @Autowired
    public UsageRecordServiceImpl(UsageRecordRepository usageRecordRepository) {
        this.usageRecordRepository = usageRecordRepository;
    }

    @Autowired
    private ToolService toolService; // 注入 ToolService

    // 实现注册使用记录
    @Override
    public String registerUsage(String toolCode, String machineCode, String taskCode, LocalDateTime startTime, LocalDateTime endTime) {
        // 获取工具当前状态
        Tool tool = toolService.getToolByCode(toolCode);
        String currentStatus = tool.getStatus();

        // 根据登记类型检查工具状态
        if (startTime != null && endTime == null) {
            // 上机时间登记：只有闲置状态可以登记
            if (!"闲置".equals(currentStatus)) {
                switch (currentStatus) {
                    case "使用中":
                        return "上机时间登记需要工具状态为闲置，当前状态为使用中";
                    case "维修中":
                        return "工具正在维修中，无法登记";
                    case "已报废":
                        return "工具已报废，无法登记";
                    default:
                        return "工具状态异常，无法登记";
                }
            }
        } else if (endTime != null && startTime == null) {
            // 下机时间登记：只有使用中状态可以登记
            if (!"使用中".equals(currentStatus)) {
                switch (currentStatus) {
                    case "闲置":
                        return "下机时间登记需要工具状态为使用中，当前状态为闲置";
                    case "维修中":
                        return "工具正在维修中，无法登记";
                    case "已报废":
                        return "工具已报废，无法登记";
                    default:
                        return "工具状态异常，无法登记";
                }
            }
        } else {
            // 同时传入开始和结束时间，或者都没传入，这种情况不应该发生
            return "登记参数错误";
        }

        // 如果传入了endTime，尝试查找匹配的未完成记录进行更新
        if (endTime != null) {
            // 查找相同工具编号、机器编号、任务编号且endTime为空的记录
            Optional<UsageRecord> existingRecord = usageRecordRepository
                .findTopByToolCodeAndMachineCodeAndTaskCodeAndEndTimeIsNullOrderByIdDesc(
                    toolCode, machineCode, taskCode);

            if (existingRecord.isPresent()) {
                // 更新现有记录的结束时间
                UsageRecord record = existingRecord.get();
                record.setEndTime(endTime);
                usageRecordRepository.save(record);

                // 下机时间登记成功后，将工具状态更新为"闲置"（系统自动更新，不记录手动变更）
                toolService.updateToolStatusInternal(toolCode, "闲置");

                return "success";
            }
        }

        // 工具状态为闲置，可以登记使用记录（新建记录）
        UsageRecord usageRecord = new UsageRecord();
        usageRecord.setToolCode(toolCode);
        usageRecord.setMachineCode(machineCode);
        usageRecord.setTaskCode(taskCode);
        usageRecord.setStartTime(startTime);  // 可能为null（下机时间登记且无匹配记录）
        usageRecord.setEndTime(endTime);      // 可能为null（上机时间登记）

        // 只有在登记上机时间时才更新工具状态为"使用中"（系统自动更新，不记录手动变更）
        // 如果只登记下机时间（startTime为null），不改变工具状态
        if (startTime != null) {
            toolService.updateToolStatusInternal(toolCode, "使用中");
        }

        usageRecordRepository.save(usageRecord);
        return "success";
    }

    // 实现获取工具使用次数
    @Override
    public long getUsageCountByToolCode(String toolCode) {
        return usageRecordRepository.countByToolCode(toolCode);
    }

    //  实现获取工具使用总时长
    @Override
    public long getTotalUsageTimeInMinutes(String toolCode) {
        Double minutes = usageRecordRepository.calculateTotalUsageTimeInMinutes(toolCode);
        return minutes.longValue(); // 将 Double 转换为 long
    }

    // 实现获取工具的所有使用记录
    @Override
    public List<UsageRecord> getUsageRecordsByToolCode(String toolCode) {
        return usageRecordRepository.findByToolCodeOrderByStartTimeDesc(toolCode);
    }
}