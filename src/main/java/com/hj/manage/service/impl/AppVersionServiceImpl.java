package com.hj.manage.service.impl;

import com.hj.manage.domain.AppVersion;
import com.hj.manage.dto.VersionCheckResult;
import com.hj.manage.repository.AppVersionRepository;
import com.hj.manage.service.AppVersionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.logging.Logger;

/**
 * 应用版本管理服务实现类
 */
@Service
@Transactional
public class AppVersionServiceImpl implements AppVersionService {

    private static final Logger logger = Logger.getLogger(AppVersionServiceImpl.class.getName());

    @Autowired
    private AppVersionRepository appVersionRepository;

    @Override
    @Transactional(readOnly = true)
    public AppVersion getLatestVersion() {
        return appVersionRepository.findTopByOrderByVersionCodeDesc()
                .orElse(null);
    }

    @Override
    @Transactional(readOnly = true)
    public VersionCheckResult checkForUpdate(Integer currentVersionCode) {
        if (currentVersionCode == null) {
            throw new IllegalArgumentException("当前版本代码不能为空");
        }

        logger.info("检查版本更新，当前版本代码: " + currentVersionCode);

        // 获取当前版本名称
        String currentVersionName = AppVersion.versionCodeToName(currentVersionCode);

        // 查找比当前版本更新的版本
        Optional<AppVersion> latestVersion = appVersionRepository
                .findLatestNewerVersion(currentVersionCode);

        if (latestVersion.isPresent()) {
            AppVersion latest = latestVersion.get();
            logger.info("发现新版本: " + latest.getVersionName());
            
            return VersionCheckResult.hasNewVersion(
                    currentVersionCode, currentVersionName,
                    latest.getVersionName(), latest.getVersionCode(),
                    latest.getDownloadUrl(), latest.getUpdateLog(), latest.getCreateTime()
            );
        } else {
            logger.info("当前已是最新版本");
            return VersionCheckResult.noNewVersion(currentVersionCode, currentVersionName);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public VersionCheckResult checkForUpdate(String currentVersionName) {
        if (currentVersionName == null || currentVersionName.trim().isEmpty()) {
            throw new IllegalArgumentException("当前版本号不能为空");
        }

        try {
            Integer currentVersionCode = AppVersion.versionNameToCode(currentVersionName);
            return checkForUpdate(currentVersionCode);
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("版本号格式错误: " + e.getMessage());
        }
    }

    @Override
    public AppVersion createNewVersion(String versionName, String downloadUrl, String updateLog) {
        if (versionName == null || versionName.trim().isEmpty()) {
            throw new IllegalArgumentException("版本号不能为空");
        }

        // 计算版本代码
        Integer versionCode = AppVersion.versionNameToCode(versionName);

        return createNewVersion(new AppVersion(versionName, versionCode, downloadUrl, updateLog));
    }

    @Override
    public AppVersion createNewVersion(AppVersion appVersion) {
        if (appVersion == null) {
            throw new IllegalArgumentException("版本信息不能为空");
        }

        // 验证版本号
        if (appVersion.getVersionName() == null || appVersion.getVersionName().trim().isEmpty()) {
            throw new IllegalArgumentException("版本号不能为空");
        }

        // 如果没有设置版本代码，自动计算
        if (appVersion.getVersionCode() == null) {
            appVersion.setVersionCode(AppVersion.versionNameToCode(appVersion.getVersionName()));
        }

        // 检查版本号是否已存在
        if (appVersionRepository.existsByVersionName(appVersion.getVersionName())) {
            throw new IllegalArgumentException("版本号 " + appVersion.getVersionName() + " 已存在");
        }

        // 检查版本代码是否已存在
        if (appVersionRepository.existsByVersionCode(appVersion.getVersionCode())) {
            throw new IllegalArgumentException("版本代码 " + appVersion.getVersionCode() + " 已存在");
        }

        logger.info("创建新版本: " + appVersion.getVersionName());

        // 保存新版本
        AppVersion savedVersion = appVersionRepository.save(appVersion);
        logger.info("新版本创建成功: " + savedVersion.getVersionName());

        return savedVersion;
    }

    @Override
    @Transactional(readOnly = true)
    public List<AppVersion> getAllVersions() {
        return appVersionRepository.findAllByOrderByVersionCodeDesc();
    }

    @Override
    @Transactional(readOnly = true)
    public AppVersion getVersionById(Integer id) {
        if (id == null) {
            throw new IllegalArgumentException("版本ID不能为空");
        }
        return appVersionRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("版本不存在，ID: " + id));
    }

    @Override
    @Transactional(readOnly = true)
    public AppVersion getVersionByName(String versionName) {
        if (versionName == null || versionName.trim().isEmpty()) {
            throw new IllegalArgumentException("版本号不能为空");
        }
        return appVersionRepository.findByVersionName(versionName)
                .orElseThrow(() -> new RuntimeException("版本不存在，版本号: " + versionName));
    }



    @Override
    public void deleteVersion(Integer id) {
        AppVersion version = getVersionById(id);

        logger.info("删除版本: " + version.getVersionName());
        appVersionRepository.deleteById(id);
        logger.info("版本删除成功");
    }

    @Override
    @Transactional(readOnly = true)
    public boolean isVersionNameExists(String versionName) {
        return appVersionRepository.existsByVersionName(versionName);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean isVersionCodeExists(Integer versionCode) {
        return appVersionRepository.existsByVersionCode(versionCode);
    }

    @Override
    @Transactional(readOnly = true)
    public String getNextSuggestedVersionName() {
        AppVersion currentActive = getLatestVersion();
        if (currentActive == null) {
            return "1.0.0";
        }
        
        // 简单的版本号递增逻辑：修订号+1
        String[] parts = currentActive.getVersionName().split("\\.");
        int patch = Integer.parseInt(parts[2]) + 1;
        
        // 如果修订号超过9，次版本号+1，修订号重置为0
        if (patch > 9) {
            int minor = Integer.parseInt(parts[1]) + 1;
            if (minor > 9) {
                int major = Integer.parseInt(parts[0]) + 1;
                return major + ".0.0";
            }
            return parts[0] + "." + minor + ".0";
        }
        
        return parts[0] + "." + parts[1] + "." + patch;
    }

    @Override
    @Transactional(readOnly = true)
    public Integer getNextSuggestedVersionCode() {
        String nextVersionName = getNextSuggestedVersionName();
        return AppVersion.versionNameToCode(nextVersionName);
    }

    @Override
    public void cleanOldVersions(int keepCount) {
        if (keepCount <= 0) {
            throw new IllegalArgumentException("保留版本数量必须大于0");
        }
        
        List<AppVersion> allVersions = getAllVersions();
        if (allVersions.size() <= keepCount) {
            logger.info("版本数量不超过保留数量，无需清理");
            return;
        }
        
        // 获取需要保留的最低版本代码
        Integer keepVersionCode = allVersions.get(keepCount - 1).getVersionCode();
        
        logger.info("清理旧版本，保留版本代码 >= " + keepVersionCode);
        appVersionRepository.deleteOldVersions(keepVersionCode);
        logger.info("旧版本清理完成");
    }

    @Override
    @Transactional(readOnly = true)
    public VersionStatistics getVersionStatistics() {
        long totalVersions = appVersionRepository.count();

        List<AppVersion> allVersions = getAllVersions();
        String latestVersion = allVersions.isEmpty() ? "无" : allVersions.get(0).getVersionName();

        return new VersionStatistics(totalVersions, latestVersion);
    }
}
