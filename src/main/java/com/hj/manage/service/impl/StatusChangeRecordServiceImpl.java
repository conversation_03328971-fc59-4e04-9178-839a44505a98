package com.hj.manage.service.impl;

import com.hj.manage.domain.StatusChangeRecord;
import com.hj.manage.repository.StatusChangeRecordRepository;
import com.hj.manage.service.StatusChangeRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Optional;

@Service
public class StatusChangeRecordServiceImpl implements StatusChangeRecordService {

    @Autowired
    private StatusChangeRecordRepository statusChangeRecordRepository;

    @Override
    public void recordManualChange(String toolCode, LocalDateTime changeTime) {
        StatusChangeRecord record = new StatusChangeRecord();
        record.setToolCode(toolCode);
        record.setChangeTime(changeTime);
        statusChangeRecordRepository.save(record);
    }

    @Override
    public Optional<StatusChangeRecord> getLatestManualChange(String toolCode) {
        return statusChangeRecordRepository.findTopByToolCodeOrderByIdDesc(toolCode);
    }
}
