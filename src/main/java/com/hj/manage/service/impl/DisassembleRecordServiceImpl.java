package com.hj.manage.service.impl;

import com.hj.manage.domain.DisassembleApply;
import com.hj.manage.domain.DisassembleRecord;
import com.hj.manage.domain.Tool;
import com.hj.manage.repository.DisassembleApplyRepository;
import com.hj.manage.repository.DisassembleRecordRepository;
import com.hj.manage.repository.ToolRepository;
import com.hj.manage.service.DisassembleRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
public class DisassembleRecordServiceImpl implements DisassembleRecordService {

    @Autowired
    private DisassembleRecordRepository disassembleRecordRepository;

    @Autowired
    private ToolRepository toolRepository;

    @Autowired
    private DisassembleApplyRepository disassembleApplyRepository;

    @Override
    @Transactional
    public DisassembleRecord createDisassembleRecord(String toolCode, String name, String disassembleInfo, LocalDateTime disassembleTime, String operator, Integer applyId) {
        // 创建并保存拆解记录
        DisassembleRecord record = new DisassembleRecord();
        record.setToolCode(toolCode);
        record.setName(name);
        record.setDisassembleInfo(disassembleInfo);
        record.setDisassembleTime(disassembleTime);
        record.setOperator(operator);

        // 更新工具状态为"已拆解"
        Optional<Tool> toolOptional = toolRepository.findByToolCode(toolCode);
        if (toolOptional.isPresent()) {
            Tool tool = toolOptional.get();
            tool.setStatus("已拆解");
            toolRepository.save(tool);
        }

        // 如果有申请ID，更新拆解申请状态为"已通过"
        if (applyId != null) {
            Optional<DisassembleApply> applyOptional = disassembleApplyRepository.findById(applyId);
            if (applyOptional.isPresent()) {
                DisassembleApply apply = applyOptional.get();
                apply.setStatus("已批准");
                disassembleApplyRepository.save(apply);
            }
        }

        return disassembleRecordRepository.save(record);
    }

    @Override
    public List<DisassembleRecord> getAllDisassembleRecords() {
        return disassembleRecordRepository.findAll();
    }
}