package com.hj.manage.service.impl;

import com.hj.manage.domain.User;
import com.hj.manage.repository.UserRepository;
import com.hj.manage.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class UserServiceImpl implements UserService {

    @Autowired
    private UserRepository userRepository;

    // 用户登录
    @Override
    public User login(String username, String password) {
        return userRepository.findByUsernameAndPassword(username, password);
    }

    // 用户添加
    @Override
    public User add(User user) {
        return userRepository.save(user);
    }

    // 重置密码
    @Override
    public User resetPassword(String username, String phone, String newPassword) {
        // 根据用户名和手机号查找用户
        User user = userRepository.findByUsernameAndPhone(username, phone);
        if (user == null) {
            throw new RuntimeException("用户名或手机号不正确");
        }
        // 更新密码
        user.setPassword(newPassword);
        return userRepository.save(user);
    }

    // 更新用户信息
    @Override
    public User updateUserInfo(Integer userId, User updatedUser) {
        // 根据用户ID查找用户
        User existingUser = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        // 更新非空字段
        if (updatedUser.getUsername() != null) {
            existingUser.setUsername(updatedUser.getUsername());
        }
        if (updatedUser.getPassword() != null) {
            existingUser.setPassword(updatedUser.getPassword());
        }
        if (updatedUser.getPhone() != null) {
            existingUser.setPhone(updatedUser.getPhone());
        }

        // 直接存储前端传递的角色值（中文）
        if (updatedUser.getRole() != null) {
            // 验证角色值是否合法
            if (!"管理人员".equals(updatedUser.getRole()) &&
                !"仓库人员".equals(updatedUser.getRole()) &&
                !"生产人员".equals(updatedUser.getRole())) {
                throw new IllegalArgumentException("无效的角色值");
            }
            existingUser.setRole(updatedUser.getRole());
        }

        // 保存更新后的用户信息
        return userRepository.save(existingUser);
    }

    //  获取所有用户
    @Override
    public List<User> getAllUsers() {
        return userRepository.findAll();
    }

    // 注册新用户
    @Override
    public User registerUser(String username, String password, String phone) {
        if (userRepository.findByUsername(username) != null) {
            throw new IllegalArgumentException("用户名已存在");
        }
        
        if (userRepository.findByPhone(phone) != null) {
            throw new IllegalArgumentException("该手机号已被注册");
        }

        User user = new User();
        user.setUsername(username);
        user.setPassword(password); // 在实际应用中，应使用密码加密
        user.setRole("普通用户"); // 默认设置为普通用户
        user.setPhone(phone);

        return userRepository.save(user);
    }
    
    // 删除用户
    @Override
    public void deleteUser(Integer userId) {
        // 检查用户是否存在
        if (!userRepository.existsById(userId)) {
            throw new RuntimeException("用户不存在");
        }
        // 删除用户
        userRepository.deleteById(userId);
    }
}