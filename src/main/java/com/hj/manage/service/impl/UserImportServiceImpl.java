package com.hj.manage.service.impl;

import com.hj.manage.domain.User;
import com.hj.manage.repository.UserRepository;
import com.hj.manage.service.UserImportService;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.DecimalFormat;
import java.util.*;

@Service
public class UserImportServiceImpl implements UserImportService {

    private final UserRepository userRepository;

    @Autowired
    public UserImportServiceImpl(UserRepository userRepository) {
        this.userRepository = userRepository;
    }

    @Override
    public Map<String, Object> importUsers(MultipartFile file) {
        Map<String, Object> result = new HashMap<>();
        List<User> successList = new ArrayList<>();
        List<Map<String, String>> duplicateList = new ArrayList<>();

        try {
            // 解析Excel文件
            Workbook workbook = new XSSFWorkbook(file.getInputStream());
            Sheet sheet = workbook.getSheetAt(0);

            // 遍历行，从第二行开始（第一行为表头）
            for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if (row == null) {
                    continue;
                }

                // 获取单元格数据
                String username = getCellValue(row.getCell(0));
                String password = getCellValue(row.getCell(1));
                String role = getCellValue(row.getCell(2));
                String phone = getCellValue(row.getCell(3));

                // 数据验证
                if (username == null || password == null || role == null) {
                    Map<String, String> errorInfo = new HashMap<>();
                    errorInfo.put("行号", String.valueOf(rowIndex + 1));
                    errorInfo.put("用户名", username != null ? username : "");
                    errorInfo.put("密码", password != null ? password : "");
                    errorInfo.put("角色", role != null ? role : "");
                    errorInfo.put("手机号", phone != null ? phone : "");
                    errorInfo.put("错误", "用户名、密码和角色不能为空");
                    duplicateList.add(errorInfo);
                    continue;
                }

                // 角色验证
                if (!"管理员".equals(role) &&
                        !"普通用户".equals(role) &&
                        !"仓库人员".equals(role) &&
                        !"生产人员".equals(role)) {
                    Map<String, String> errorInfo = new HashMap<>();
                    errorInfo.put("行号", String.valueOf(rowIndex + 1));
                    errorInfo.put("用户名", username);
                    errorInfo.put("密码", password);
                    errorInfo.put("角色", role);
                    errorInfo.put("手机号", phone != null ? phone : "");
                    errorInfo.put("错误", "无效的角色值，必须是：管理员、普通用户、仓库人员或生产人员");
                    duplicateList.add(errorInfo);
                    continue;
                }

                // 检查用户名是否已存在
                User existingUserByUsername = userRepository.findByUsername(username);
                if (existingUserByUsername != null) {
                    Map<String, String> duplicateInfo = new HashMap<>();
                    duplicateInfo.put("行号", String.valueOf(rowIndex + 1));
                    duplicateInfo.put("用户名", username);
                    duplicateInfo.put("密码", password);
                    duplicateInfo.put("角色", role);
                    duplicateInfo.put("手机号", phone != null ? phone : "");
                    duplicateInfo.put("错误", "用户名已存在");
                    duplicateList.add(duplicateInfo);
                    continue;
                }

                // 检查手机号是否已存在（如果提供了手机号）
                if (phone != null && !phone.isEmpty()) {
                    User existingUserByPhone = userRepository.findByPhone(phone);
                    if (existingUserByPhone != null) {
                        Map<String, String> duplicateInfo = new HashMap<>();
                        duplicateInfo.put("行号", String.valueOf(rowIndex + 1));
                        duplicateInfo.put("用户名", username);
                        duplicateInfo.put("密码", password);
                        duplicateInfo.put("角色", role);
                        duplicateInfo.put("手机号", phone);
                        duplicateInfo.put("错误", "手机号已被注册");
                        duplicateList.add(duplicateInfo);
                        continue;
                    }
                }

                // 创建新用户
                User newUser = new User();
                newUser.setUsername(username);
                newUser.setPassword(password);
                newUser.setRole(role);
                newUser.setPhone(phone);

                // 保存用户
                User savedUser = userRepository.save(newUser);
                successList.add(savedUser);
            }

            workbook.close();

            // 组装返回结果
            result.put("success", true);
            result.put("successCount", successList.size());
            result.put("successList", successList);
            result.put("duplicateCount", duplicateList.size());
            result.put("duplicateList", duplicateList);

            return result;
        } catch (IOException e) {
            result.put("success", false);
            result.put("message", "文件解析失败: " + e.getMessage());
            return result;
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "导入过程发生错误: " + e.getMessage());
            return result;
        }
    }

    /**
     * 获取单元格的值
     * @param cell 单元格
     * @return 单元格的字符串值
     */
    private String getCellValue(Cell cell) {
        if (cell == null) {
            return null;
        }
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                // 使用DecimalFormat格式化数字，确保不使用科学计数法
                DecimalFormat df = new DecimalFormat("0");
                return df.format(cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return null;
        }
    }
}