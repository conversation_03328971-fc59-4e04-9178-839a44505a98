package com.hj.manage.service.impl;

import com.hj.manage.domain.DisassembleApply;
import com.hj.manage.repository.DisassembleApplyRepository;
import com.hj.manage.service.DisassembleApplyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class DisassembleApplyServiceImpl implements DisassembleApplyService {

    @Autowired
    private DisassembleApplyRepository disassembleApplyRepository;

    // 提交拆解申请
    @Override
    public DisassembleApply submitDisassembleApplication(String toolCode, LocalDateTime applyTime, String reason, String imageUrl, String operator) {
        DisassembleApply application = new DisassembleApply();
        application.setToolCode(toolCode);
        application.setApplyTime(applyTime);
        application.setReason(reason);
        application.setImageUrl(imageUrl);
        application.setOperator(operator);
        application.setStatus("待审批"); // 默认状态为待审批

        return disassembleApplyRepository.save(application);
    }

    // 同意拆解申请 - 不再更新工具状态，只更新申请状态
    @Override
    public DisassembleApply approveDisassembleApplication(Integer applyId) {
        // 根据拆解申请ID查找对应的拆解申请
        DisassembleApply application = disassembleApplyRepository.findById(applyId)
                .orElseThrow(() -> new RuntimeException("未找到ID为 " + applyId + " 的拆解申请！"));

        // 更新拆解申请的状态为"已通过"
        application.setStatus("已通过");

        // 保存更新后的拆解申请
        return disassembleApplyRepository.save(application);
    }

    // 驳回拆解申请
    @Override
    public DisassembleApply rejectDisassembleApplication(Integer applyId) {
        // 根据拆解申请ID查找对应的拆解申请
        DisassembleApply application = disassembleApplyRepository.findById(applyId)
                .orElseThrow(() -> new RuntimeException("未找到ID为 " + applyId + " 的拆解申请！"));

        // 更新拆解申请的状态为"未通过"
        application.setStatus("未通过");

        // 保存更新后的拆解申请
        return disassembleApplyRepository.save(application);
    }

    // 获取所有待审批的拆解申请
    @Override
    public List<DisassembleApply> getAllPendingDisassembleApplications() {
        // 查询状态为"待审批"的拆解申请
        return disassembleApplyRepository.findByStatus("待审批");
    }

    // 获取所有拆解申请记录（包括所有状态）
    @Override
    public List<DisassembleApply> getAllDisassembleApplications() {
        // 查询所有拆解申请记录，不按状态过滤
        return disassembleApplyRepository.findAll();
    }
}