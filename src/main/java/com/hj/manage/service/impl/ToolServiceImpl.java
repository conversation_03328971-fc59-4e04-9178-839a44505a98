package com.hj.manage.service.impl;

import com.hj.manage.domain.StatusChangeRecord;
import com.hj.manage.domain.Tool;
import com.hj.manage.domain.UsageRecord;
import com.hj.manage.repository.StatusChangeRecordRepository;
import com.hj.manage.repository.ToolRepository;
import com.hj.manage.repository.UsageRecordRepository;
import com.hj.manage.service.ToolService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.logging.Logger;

@Service
public class ToolServiceImpl implements ToolService {

    private static final Logger logger = Logger.getLogger(ToolServiceImpl.class.getName());

    @Autowired
    private ToolRepository toolRepository;

    @Autowired
    private UsageRecordRepository usageRecordRepository;

    @Autowired
    private StatusChangeRecordRepository statusChangeRecordRepository;

    // 获取所有工具
    @Override
    public List<Tool> getAllTools() {
        return toolRepository.findAll();
    }

    //  添加工具
    @Override
    public Tool addTool(String toolCode, String name, String model, String specification,
                        String supplier, Date purchaseDate, BigDecimal price) {
        // 创建一个新的工具对象
        Tool tool = new Tool();
        tool.setToolCode(toolCode);
        tool.setName(name);
        tool.setModel(model);
        tool.setSpecification(specification);
        tool.setSupplier(supplier);
        tool.setPurchaseDate(purchaseDate);
        tool.setPrice(price);
        tool.setStatus("闲置"); // 默认状态为"闲置"

        // 保存到数据库
        return toolRepository.save(tool);
    }

    // 更新工具状态（用于手动状态变更，会记录变更记录）
    @Override
    public Tool updateToolStatus(String toolCode, String newStatus) {
        return updateToolStatusInternal(toolCode, newStatus);
    }

    // 系统内部更新工具状态（不记录手动变更记录）
    @Override
    public Tool updateToolStatusInternal(String toolCode, String newStatus) {
        // 根据工具编号查找工具
        Optional<Tool> optionalTool = toolRepository.findByToolCode(toolCode);
        if (optionalTool.isPresent()) {
            Tool tool = optionalTool.get();
            tool.setStatus(newStatus); // 更新状态
            return toolRepository.save(tool); // 保存到数据库
        } else {
            throw new RuntimeException("未找到工具编号为 " + toolCode + " 的工具！");
        }
    }

    @Override
    public Tool getToolByCode(String toolCode) {
        // 调用Repository中的findByToolCode方法
        Optional<Tool> toolOptional = toolRepository.findByToolCode(toolCode);

        // 如果找不到对应的工具，抛出异常或返回null（根据需求选择）
        return toolOptional.orElseThrow(() -> new RuntimeException("未找到工具编号为 " + toolCode + " 的工具"));
    }

    //  计算拆解率
    @Override
    public Object calculateDisassembleRate(String name) {
        // 首先检查是否存在该工具
        Long totalCount = toolRepository.countTotalToolsByName(name);
        if (totalCount == 0) {
            return new ToolDisassembleRateResponse(false, "未找到名称为 " + name + " 的工具", null);
        }

        Long disassembledCount = toolRepository.countDisassembledToolsByName(name);
        Double rate = ((double) disassembledCount / totalCount) * 100;
        return new ToolDisassembleRateResponse(true, null, rate);
    }

    // 检查并更新所有工具的状态
    @Override
    @Transactional
    public void updateToolStatusBasedOnUsageRecords() {
        LocalDateTime currentTime = LocalDateTime.now();
        logger.info("开始检查工具状态更新 - 当前时间: " + currentTime);

        try {
            // 获取所有需要更新状态的工具编号
            List<String> toolCodesToUpdate = usageRecordRepository.findToolCodesWithExpiredUsage(currentTime);

            logger.info("找到 " + toolCodesToUpdate.size() + " 个需要更新状态的工具");

            // 更新每个工具的状态
            for (String toolCode : toolCodesToUpdate) {
                // 调用单个工具状态检查方法，这样可以复用手动变更检查逻辑
                checkAndUpdateSingleToolStatus(toolCode);
            }
        } catch (Exception e) {
            logger.severe("更新工具状态时发生错误: " + e.getMessage());
            throw e;
        }
    }

    // 检查并更新单个工具的状态
    @Override
    @Transactional
    public void checkAndUpdateSingleToolStatus(String toolCode) {
        LocalDateTime currentTime = LocalDateTime.now();
        logger.info("检查工具 " + toolCode + " 的状态 - 当前时间: " + currentTime);

        try {
            Optional<Tool> optionalTool = toolRepository.findByToolCode(toolCode);
            if (optionalTool.isPresent()) {
                Tool tool = optionalTool.get();

                // 只检查状态为"使用中"的工具
                if ("使用中".equals(tool.getStatus())) {
                    // 获取该工具的最新使用记录（按创建时间排序）
                    Optional<UsageRecord> latestRecord = usageRecordRepository.findTopByToolCodeOrderByIdDesc(toolCode);
                    // 获取该工具的最新手动状态变更记录
                    Optional<StatusChangeRecord> lastManualChange = statusChangeRecordRepository.findTopByToolCodeOrderByIdDesc(toolCode);

                    if (latestRecord.isPresent()) {
                        UsageRecord record = latestRecord.get();

                        // 只有当最新记录有结束时间且已过期时，才考虑自动变更状态
                        if (record.getEndTime() != null && record.getEndTime().isBefore(currentTime)) {

                            // 检查是否有手动变更记录，且手动变更时间晚于使用记录的结束时间
                            if (lastManualChange.isPresent() &&
                                lastManualChange.get().getChangeTime().isAfter(record.getEndTime())) {
                                logger.info("工具 " + toolCode + " 有更新的手动状态变更（变更时间: " +
                                    lastManualChange.get().getChangeTime() + " 晚于使用记录结束时间: " +
                                    record.getEndTime() + "），跳过自动状态更新");
                                return; // 不进行自动变更
                            }

                            logger.info("更新工具 " + toolCode + " 状态为'闲置' (最新使用记录结束时间: " + record.getEndTime() + ")");
                            // 使用内部方法更新状态，不记录手动变更
                            updateToolStatusInternal(toolCode, "闲置");
                        }
                        // 如果最新记录没有结束时间（只有上机时间），则不自动变更状态
                    }
                }
            }
        } catch (Exception e) {
            logger.severe("更新工具 " + toolCode + " 状态时发生错误: " + e.getMessage());
            throw e;
        }
    }

    // 内部类用于返回拆解率计算结果
    private static class ToolDisassembleRateResponse {
        private final boolean success;
        private final String message;
        private final Double rate;

        public ToolDisassembleRateResponse(boolean success, String message, Double rate) {
            this.success = success;
            this.message = message;
            this.rate = rate;
        }

        public boolean isSuccess() {
            return success;
        }

        public String getMessage() {
            return message;
        }

        public Double getRate() {
            return rate;
        }
    }

}