package com.hj.manage.service.impl;

import com.hj.manage.domain.Tool;
import com.hj.manage.repository.ToolRepository;
import com.hj.manage.service.ToolImportService;
import com.hj.manage.utils.ResponseUtil;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class ToolImportServiceImpl implements ToolImportService {

    @Autowired
    private ToolRepository toolRepository;

    @Override
    public Map<String, Object> importTools(MultipartFile file) {
        List<Tool> validTools = new ArrayList<>();
        List<Map<String, Object>> errorRecords = new ArrayList<>();
        Set<String> duplicateCodes = new HashSet<>();

        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);
            Iterator<Row> rows = sheet.iterator();

            // 跳过表头
            if (rows.hasNext()) {
                rows.next();
            }

            while (rows.hasNext()) {
                Row row = rows.next();
                String toolCode = getStringCellValue(row.getCell(0));

                if (toolCode == null || toolCode.trim().isEmpty()) {
                    continue; // 跳过空行或无编号的数据
                }

                // 检查记录是否有效
                boolean isValid = true;
                Map<String, Object> errorRecord = new HashMap<>();
                errorRecord.put("toolCode", toolCode);
                errorRecord.put("name", getStringCellValue(row.getCell(1)));
                errorRecord.put("model", getStringCellValue(row.getCell(2)));
                errorRecord.put("specification", getStringCellValue(row.getCell(3)));
                errorRecord.put("supplier", getStringCellValue(row.getCell(4)));

                String dateString = getStringCellValue(row.getCell(5));
                errorRecord.put("purchaseDate", dateString);
                errorRecord.put("price", getNumericCellValue(row.getCell(6)));

                // 创建错误原因
                List<String> errorReasons = new ArrayList<>();

                // 检查编号是否重复
                if (toolRepository.existsByToolCode(toolCode)) {
                    duplicateCodes.add(toolCode);
                    errorReasons.add("编号重复");
                    isValid = false;
                }

                // 检查日期是否有效
                Date purchaseDate = null;
                try {
                    if (dateString != null && !dateString.trim().isEmpty()) {
                        purchaseDate = parseDate(dateString);
                    }
                } catch (RuntimeException e) {
                    // 日期格式无效
                    errorReasons.add(e.getMessage());
                    isValid = false;
                }

                if (isValid) {
                    // 所有检查都通过，创建有效的工具对象
                    Tool tool = new Tool();
                    tool.setToolCode(toolCode);
                    tool.setName(getStringCellValue(row.getCell(1)));
                    tool.setModel(getStringCellValue(row.getCell(2)));
                    tool.setSpecification(getStringCellValue(row.getCell(3)));
                    tool.setSupplier(getStringCellValue(row.getCell(4)));
                    tool.setPurchaseDate(purchaseDate);
                    tool.setPrice(getNumericCellValue(row.getCell(6)));
                    tool.setStatus("闲置"); // 默认状态为"闲置"

                    validTools.add(tool);
                } else {
                    // 记录有一个或多个错误
                    errorRecord.put("errorReasons", String.join("；", errorReasons));
                    errorRecords.add(errorRecord);
                }
            }

            // 如果有有效数据才保存
            if (!validTools.isEmpty()) {
                try {
                    toolRepository.saveAll(validTools);
                } catch (DataIntegrityViolationException e) {
                    // 如果批量保存出错，尝试逐个保存以获取最大的成功数量
                    validTools.clear();
                    return ResponseUtil.error("数据库约束错误：可能包含重复数据，请检查后再试");
                }
            }

        } catch (IOException e) {
            return ResponseUtil.error("文件读取失败: " + e.getMessage());
        } catch (Exception e) {
            return ResponseUtil.error("导入过程中发生错误: " + e.getMessage());
        }

        // 构建结果响应
        Map<String, Object> result = new HashMap<>();
        result.put("success", errorRecords.isEmpty());
        result.put("successCount", validTools.size());

        if (!errorRecords.isEmpty()) {
            result.put("status", "warning");
            result.put("message", "部分数据导入失败");
            result.put("errorCount", errorRecords.size());
            result.put("errorRecords", errorRecords);

            // 统计不同类型的错误
            long dateErrors = errorRecords.stream()
                    .filter(rec -> rec.get("errorReasons").toString().contains("日期"))
                    .count();

            long duplicateErrors = errorRecords.stream()
                    .filter(rec -> rec.get("errorReasons").toString().contains("编号重复"))
                    .count();

            Map<String, Long> errorStats = new HashMap<>();
            if (dateErrors > 0) {
                errorStats.put("dateErrors", dateErrors);
            }
            if (duplicateErrors > 0) {
                errorStats.put("duplicateErrors", duplicateErrors);
            }

            result.put("errorStats", errorStats);
        } else if (validTools.isEmpty()) {
            result.put("success", false);
            result.put("status", "error");
            result.put("message", "没有有效数据导入");
        } else {
            result.put("message", "成功导入 " + validTools.size() + " 条工具数据");
        }

        return result;
    }

    // 支持多种日期格式解析
    private Date parseDate(String dateString) {
        if (dateString == null || dateString.trim().isEmpty()) {
            return null;
        }

        String[] patterns = {
                "yyyy-MM-dd",
                "yyyy.MM.dd",
                "dd/MM/yyyy",
                "MM/dd/yyyy"
        };

        for (String pattern : patterns) {
            SimpleDateFormat sdf = new SimpleDateFormat(pattern);
            sdf.setLenient(false); // 严格匹配格式
            try {
                return sdf.parse(dateString);
            } catch (ParseException ignored) {
                // 继续尝试下一个格式
            }
        }

        throw new RuntimeException("无效的日期: " + dateString + "（该日期不存在）");
    }

    private String getStringCellValue(Cell cell) {
        if (cell == null) {
            return null;
        }
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    return sdf.format(cell.getDateCellValue());
                } else {
                    return String.valueOf((int) cell.getNumericCellValue());
                }
            default:
                return null;
        }
    }

    private BigDecimal getNumericCellValue(Cell cell) {
        if (cell == null) {
            return null;
        }
        if (cell.getCellType() == CellType.NUMERIC) {
            return BigDecimal.valueOf(cell.getNumericCellValue());
        }
        return null;
    }
}