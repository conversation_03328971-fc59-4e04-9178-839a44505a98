package com.hj.manage.service;

import com.hj.manage.domain.AppVersion;
import com.hj.manage.dto.VersionCheckResult;

import java.util.List;

/**
 * 应用版本管理服务接口
 */
public interface AppVersionService {

    /**
     * 获取最新版本
     * @return 最新版本
     */
    AppVersion getLatestVersion();

    /**
     * 检查版本更新
     * @param currentVersionCode 当前版本代码
     * @return 版本检查结果
     */
    VersionCheckResult checkForUpdate(Integer currentVersionCode);

    /**
     * 检查版本更新（通过版本号）
     * @param currentVersionName 当前版本号
     * @return 版本检查结果
     */
    VersionCheckResult checkForUpdate(String currentVersionName);

    /**
     * 创建新版本
     * @param versionName 版本号
     * @param downloadUrl 下载链接
     * @param updateLog 更新日志
     * @return 创建的版本
     */
    AppVersion createNewVersion(String versionName, String downloadUrl, String updateLog);

    /**
     * 创建新版本
     * @param appVersion 版本对象
     * @return 创建的版本
     */
    AppVersion createNewVersion(AppVersion appVersion);

    /**
     * 获取所有版本列表
     * @return 版本列表
     */
    List<AppVersion> getAllVersions();

    /**
     * 根据ID获取版本
     * @param id 版本ID
     * @return 版本信息
     */
    AppVersion getVersionById(Integer id);

    /**
     * 根据版本号获取版本
     * @param versionName 版本号
     * @return 版本信息
     */
    AppVersion getVersionByName(String versionName);

    /**
     * 删除版本
     * @param id 版本ID
     */
    void deleteVersion(Integer id);

    /**
     * 检查版本号是否已存在
     * @param versionName 版本号
     * @return 是否存在
     */
    boolean isVersionNameExists(String versionName);

    /**
     * 检查版本代码是否已存在
     * @param versionCode 版本代码
     * @return 是否存在
     */
    boolean isVersionCodeExists(Integer versionCode);

    /**
     * 获取下一个建议的版本号
     * @return 建议的版本号
     */
    String getNextSuggestedVersionName();

    /**
     * 获取下一个建议的版本代码
     * @return 建议的版本代码
     */
    Integer getNextSuggestedVersionCode();

    /**
     * 清理旧版本（保留最近的指定数量版本）
     * @param keepCount 保留的版本数量
     */
    void cleanOldVersions(int keepCount);

    /**
     * 获取版本统计信息
     * @return 统计信息
     */
    VersionStatistics getVersionStatistics();

    /**
     * 版本统计信息内部类
     */
    class VersionStatistics {
        private long totalVersions;
        private String latestVersion;

        public VersionStatistics(long totalVersions, String latestVersion) {
            this.totalVersions = totalVersions;
            this.latestVersion = latestVersion;
        }

        // Getters
        public long getTotalVersions() { return totalVersions; }
        public String getLatestVersion() { return latestVersion; }
    }
}
