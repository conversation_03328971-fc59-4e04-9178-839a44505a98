package com.hj.manage.service;

import com.hj.manage.domain.DisassembleApply;

import java.time.LocalDateTime;
import java.util.List;

public interface DisassembleApplyService {

    // 提交拆解申请
    DisassembleApply submitDisassembleApplication(String toolCode, LocalDateTime applyTime, String reason, String imageUrl, String operator);

    // 同意拆解申请 - 不再更新工具状态，只更新申请状态
    DisassembleApply approveDisassembleApplication(Integer applyId);

    // 拒绝拆解申请
    DisassembleApply rejectDisassembleApplication(Integer applyId);

    // 获取所有待处理的拆解申请
    List<DisassembleApply> getAllPendingDisassembleApplications();

    // 获取所有拆解申请记录（包括所有状态）
    List<DisassembleApply> getAllDisassembleApplications();

}