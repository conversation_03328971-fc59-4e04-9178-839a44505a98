package com.hj.manage.service;

import com.hj.manage.domain.DisassembleRecord;

import java.time.LocalDateTime;
import java.util.List;

public interface DisassembleRecordService {

    // 创建拆解记录
    DisassembleRecord createDisassembleRecord(String toolCode, String name, String disassembleInfo, LocalDateTime disassembleTime, String operator, Integer applyId);

    // 获取所有拆解记录
    List<DisassembleRecord> getAllDisassembleRecords();
}