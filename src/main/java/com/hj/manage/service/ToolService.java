package com.hj.manage.service;

import com.hj.manage.domain.Tool;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public interface ToolService {

    //  获取所有工具
    List<Tool> getAllTools();

    // 添加工具
    Tool addTool(String toolCode, String name, String model, String specification,
                 String supplier, Date purchaseDate, BigDecimal price);

    // 更新工具状态（用于手动状态变更，会记录变更记录）
    Tool updateToolStatus(String toolCode, String newStatus);

    // 系统内部更新工具状态（不记录手动变更记录）
    Tool updateToolStatusInternal(String toolCode, String newStatus);

    Tool getToolByCode(String toolCode);

    // 计算拆解率
    Object calculateDisassembleRate(String name);

    // 检查并更新所有工具的状态
    void updateToolStatusBasedOnUsageRecords();

    // 检查并更新单个工具的状态
    void checkAndUpdateSingleToolStatus(String toolCode);
}