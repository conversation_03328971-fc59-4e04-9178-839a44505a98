package com.hj.manage.task;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.nio.file.Files;

@Component
public class DatabaseBackupScheduler {

    @Value("${mysql.dump.path}")
    private String MYSQL_DUMP_PATH;

    @Value("${mysql.username}")
    private String USERNAME;

    @Value("${mysql.password}")
    private String PASSWORD;

    @Value("${mysql.database}")
    private String DATABASE;

    @Value("${mysql.backup.path}")
    private String BACKUP_PATH;

    @Scheduled(cron = "0 0 2 ? * SUN") // 每周日凌晨2点执行
    public void backupDatabase() {
        File backupDir = new File(BACKUP_PATH);
        if (!backupDir.exists()) {
            try {
                Files.createDirectories(backupDir.toPath());
            } catch (Exception e) {
                e.printStackTrace();
                return;
            }
        }

        try {
            ProcessBuilder processBuilder = new ProcessBuilder(
                    MYSQL_DUMP_PATH,
                    "-u", USERNAME,
                    "-p" + PASSWORD,
                    DATABASE,
                    "--result-file", BACKUP_PATH + "\\db_backup_" + System.currentTimeMillis() + ".sql");
            Process process = processBuilder.start();
            int exitCode = process.waitFor();
            if (exitCode == 0) {
                System.out.println("数据库备份成功！");
            } else {
                BufferedReader reader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
                String line;
                while ((line = reader.readLine()) != null) {
                    System.err.println(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}