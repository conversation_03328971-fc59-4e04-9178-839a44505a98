package com.hj.manage.domain;

import lombok.Data;
import javax.persistence.*;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "disassemble_record", schema = "manage")
public class DisassembleRecord {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "tool_code", nullable = false)
    private String toolCode;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "disassemble_info")
    private String disassembleInfo;

    @Column(name = "disassemble_time")
    private LocalDateTime disassembleTime;

    @Column(name = "operator")
    private String operator;
}