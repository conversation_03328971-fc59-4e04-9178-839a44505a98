package com.hj.manage.domain;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 应用版本管理实体类
 * 用于管理应用版本发布和更新通知
 */
@Entity
@Table(name = "app_version")
public class AppVersion {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 版本号，格式如：1.0.1
     */
    @Column(name = "version_name", nullable = false, unique = true, length = 20)
    private String versionName;

    /**
     * 版本代码，用于版本比较，如：101
     */
    @Column(name = "version_code", nullable = false, unique = true)
    private Integer versionCode;

    /**
     * 下载链接
     */
    @Column(name = "download_url", length = 500)
    private String downloadUrl;

    /**
     * 更新日志
     */
    @Column(name = "update_log", columnDefinition = "TEXT")
    private String updateLog;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;

    /**
     * 在持久化之前自动设置创建时间
     */
    @PrePersist
    protected void onCreate() {
        if (createTime == null) {
            createTime = LocalDateTime.now();
        }
    }

    /**
     * 构造函数
     */
    public AppVersion() {}

    public AppVersion(String versionName, Integer versionCode, String downloadUrl, String updateLog) {
        this.versionName = versionName;
        this.versionCode = versionCode;
        this.downloadUrl = downloadUrl;
        this.updateLog = updateLog;
    }

    // Getters and Setters
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getVersionName() {
        return versionName;
    }

    public void setVersionName(String versionName) {
        this.versionName = versionName;
    }

    public Integer getVersionCode() {
        return versionCode;
    }

    public void setVersionCode(Integer versionCode) {
        this.versionCode = versionCode;
    }

    public String getDownloadUrl() {
        return downloadUrl;
    }

    public void setDownloadUrl(String downloadUrl) {
        this.downloadUrl = downloadUrl;
    }

    public String getUpdateLog() {
        return updateLog;
    }

    public void setUpdateLog(String updateLog) {
        this.updateLog = updateLog;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    /**
     * 版本号转换为版本代码的工具方法
     * 格式：主版本号 * 100 + 次版本号 * 10 + 修订号
     * 例如：1.0.1 → 101
     */
    public static Integer versionNameToCode(String versionName) {
        if (versionName == null || versionName.trim().isEmpty()) {
            throw new IllegalArgumentException("版本号不能为空");
        }
        
        String[] parts = versionName.split("\\.");
        if (parts.length != 3) {
            throw new IllegalArgumentException("版本号格式错误，应为 x.x.x 格式");
        }
        
        try {
            int major = Integer.parseInt(parts[0]);
            int minor = Integer.parseInt(parts[1]);
            int patch = Integer.parseInt(parts[2]);
            
            if (major < 0 || minor < 0 || patch < 0) {
                throw new IllegalArgumentException("版本号各部分不能为负数");
            }
            
            if (major > 99 || minor > 9 || patch > 9) {
                throw new IllegalArgumentException("版本号超出范围：主版本号≤99，次版本号≤9，修订号≤9");
            }
            
            return major * 100 + minor * 10 + patch;
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("版本号格式错误，必须为数字");
        }
    }

    /**
     * 版本代码转换为版本号的工具方法
     * 例如：101 → 1.0.1
     */
    public static String versionCodeToName(Integer versionCode) {
        if (versionCode == null || versionCode < 0) {
            throw new IllegalArgumentException("版本代码不能为空或负数");
        }
        
        int major = versionCode / 100;
        int minor = (versionCode % 100) / 10;
        int patch = versionCode % 10;
        
        return String.format("%d.%d.%d", major, minor, patch);
    }

    /**
     * 比较版本号大小
     * @param otherVersionCode 其他版本代码
     * @return 1: 当前版本更新, 0: 版本相同, -1: 当前版本更旧
     */
    public int compareVersion(Integer otherVersionCode) {
        if (otherVersionCode == null) {
            return 1;
        }
        return Integer.compare(this.versionCode, otherVersionCode);
    }

    @Override
    public String toString() {
        return "AppVersion{" +
                "id=" + id +
                ", versionName='" + versionName + '\'' +
                ", versionCode=" + versionCode +
                ", downloadUrl='" + downloadUrl + '\'' +
                ", updateLog='" + updateLog + '\'' +
                ", createTime=" + createTime +
                '}';
    }
}
