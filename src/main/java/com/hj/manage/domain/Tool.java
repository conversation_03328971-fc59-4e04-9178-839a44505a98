package com.hj.manage.domain;

import lombok.Data;
import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Entity
@Table(name = "tool",schema = "manage")
public class Tool {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "tool_code", nullable = false, unique = true)
    private String toolCode;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "model")
    private String model;

    @Column(name = "specification")
    private String specification;

    @Column(name = "supplier")
    private String supplier;

    @Column(name = "purchase_date")
    private Date purchaseDate;

    @Column(name = "price", precision = 10, scale = 2)
    private BigDecimal price;

    @Column(name = "status", nullable = false)
    private String status;
}