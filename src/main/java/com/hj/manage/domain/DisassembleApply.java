package com.hj.manage.domain;

import lombok.Data;
import javax.persistence.*;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "disassemble_apply", schema = "manage")
public class DisassembleApply {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "tool_code", nullable = false)
    private String toolCode;

    @Column(name = "apply_time")
    private LocalDateTime applyTime;

    @Column(name = "reason")
    private String reason;

    @Column(name = "image_url")
    private String imageUrl;

    @Column(name = "operator")
    private String operator;

    @Column(name = "status", nullable = false)
    private String status;
}