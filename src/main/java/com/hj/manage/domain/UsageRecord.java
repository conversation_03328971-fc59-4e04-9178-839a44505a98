package com.hj.manage.domain;

import lombok.Data;
import javax.persistence.*;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "usage_record")
public class UsageRecord {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "tool_code", nullable = false)
    private String toolCode;

    @Column(name = "machine_code", nullable = false)
    private String machineCode;

    @Column(name = "task_code", nullable = false)
    private String taskCode;

    @Column(name = "start_time")
    private LocalDateTime startTime;

    @Column(name = "end_time")
    private LocalDateTime endTime;
}