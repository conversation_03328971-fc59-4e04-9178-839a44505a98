package com.hj.manage.domain;

import lombok.Data;
import javax.persistence.*;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "status_change_record")
public class StatusChangeRecord {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "tool_code", nullable = false)
    private String toolCode;

    @Column(name = "change_time", nullable = false)
    private LocalDateTime changeTime;
}
