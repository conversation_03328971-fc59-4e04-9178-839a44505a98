package com.hj.manage.repository;

import com.hj.manage.domain.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface UserRepository extends JpaRepository<User, Integer> {

    // 根据用户名和密码查询用户
    User findByUsernameAndPassword(String username, String password);

    //  根据用户名和手机号查询用户
    User findByUsernameAndPhone(String username, String phone);

    //  根据用户名查询用户
    User findByUsername(String username);
    
    //  根据手机号查询用户
    User findByPhone(String phone);
}