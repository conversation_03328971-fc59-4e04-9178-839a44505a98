package com.hj.manage.repository;

import com.hj.manage.domain.UsageRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface UsageRecordRepository extends JpaRepository<UsageRecord, Integer> {

    //  获取工具的使用次数
    long countByToolCode(String toolCode);

    // 获取工具的使用总时长
    @Query(value = "SELECT COALESCE(SUM(TIMESTAMPDIFF(MINUTE, u.start_time, u.end_time)), 0) FROM usage_record u WHERE u.tool_code = :toolCode AND u.end_time IS NOT NULL", nativeQuery = true)
    Double calculateTotalUsageTimeInMinutes(@Param("toolCode") String toolCode);

    // 根据工具编号查询所有使用记录
    List<UsageRecord> findByToolCodeOrderByStartTimeDesc(String toolCode);

    // 查找工具的最新使用记录（按结束时间排序）
    Optional<UsageRecord> findTopByToolCodeOrderByEndTimeDesc(String toolCode);

    // 查找工具的最新使用记录（按ID排序，即创建时间）
    Optional<UsageRecord> findTopByToolCodeOrderByIdDesc(String toolCode);

    // 查找指定工具、机器、任务且结束时间为空的最新记录
    Optional<UsageRecord> findTopByToolCodeAndMachineCodeAndTaskCodeAndEndTimeIsNullOrderByIdDesc(
        String toolCode, String machineCode, String taskCode);

    // 查找所有状态为"使用中"的工具编号，这些工具的最新使用记录的结束时间已过期
    // 修改逻辑：只有最新记录（按ID排序）有结束时间且已过期的工具才会被返回
    @Query("SELECT DISTINCT u.toolCode FROM UsageRecord u WHERE u.toolCode IN " +
            "(SELECT t.toolCode FROM Tool t WHERE t.status = '使用中') " +
            "AND u.endTime IS NOT NULL " +
            "AND u.endTime < :currentTime " +
            "AND u.id = (SELECT MAX(u2.id) FROM UsageRecord u2 WHERE u2.toolCode = u.toolCode)")
    List<String> findToolCodesWithExpiredUsage(@Param("currentTime") LocalDateTime currentTime);
}