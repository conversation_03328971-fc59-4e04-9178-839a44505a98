package com.hj.manage.repository;

import com.hj.manage.domain.AppVersion;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 应用版本管理数据访问层
 */
@Repository
public interface AppVersionRepository extends JpaRepository<AppVersion, Integer> {

    /**
     * 获取最新版本
     * @return 最新版本
     */
    Optional<AppVersion> findTopByOrderByVersionCodeDesc();

    /**
     * 根据版本号查询
     * @param versionName 版本号
     * @return 版本信息
     */
    Optional<AppVersion> findByVersionName(String versionName);

    /**
     * 根据版本代码查询
     * @param versionCode 版本代码
     * @return 版本信息
     */
    Optional<AppVersion> findByVersionCode(Integer versionCode);

    /**
     * 检查版本号是否已存在
     * @param versionName 版本号
     * @return 是否存在
     */
    boolean existsByVersionName(String versionName);

    /**
     * 检查版本代码是否已存在
     * @param versionCode 版本代码
     * @return 是否存在
     */
    boolean existsByVersionCode(Integer versionCode);

    /**
     * 获取所有版本列表，按版本代码倒序排列
     * @return 版本列表
     */
    List<AppVersion> findAllByOrderByVersionCodeDesc();

    /**
     * 检查是否有比指定版本代码更新的版本
     * @param versionCode 当前版本代码
     * @return 更新的版本列表
     */
    @Query("SELECT v FROM AppVersion v WHERE v.versionCode > :versionCode ORDER BY v.versionCode DESC")
    List<AppVersion> findNewerVersions(@Param("versionCode") Integer versionCode);

    /**
     * 获取比指定版本代码更新的最新版本（按创建时间排序）
     * @param versionCode 当前版本代码
     * @return 最新的更新版本
     */
    @Query(value = "SELECT * FROM app_version WHERE version_code > :versionCode ORDER BY create_time DESC LIMIT 1", nativeQuery = true)
    Optional<AppVersion> findLatestNewerVersion(@Param("versionCode") Integer versionCode);

    /**
     * 获取版本总数
     * @return 版本总数
     */
    long count();

    /**
     * 删除指定版本代码之前的旧版本（用于数据清理）
     * @param keepVersionCode 保留的最低版本代码
     */
    @Modifying
    @Query("DELETE FROM AppVersion v WHERE v.versionCode < :keepVersionCode")
    void deleteOldVersions(@Param("keepVersionCode") Integer keepVersionCode);
}
