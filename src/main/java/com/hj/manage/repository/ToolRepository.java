package com.hj.manage.repository;

import com.hj.manage.domain.Tool;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.Optional;

public interface ToolRepository extends JpaRepository<Tool, Integer> {

    // 根据工具编号查询工具
    Optional<Tool> findByToolCode(String toolCode);

    // 新增这个方法用于判断是否存在重复 toolCode
    Boolean existsByToolCode(String toolCode);

    @Query("SELECT COUNT(t) FROM Tool t WHERE t.name = :name AND t.status = '已拆解'")
    Long countDisassembledToolsByName(String name);

    @Query("SELECT COUNT(t) FROM Tool t WHERE t.name = :name")
    Long countTotalToolsByName(String name);

}