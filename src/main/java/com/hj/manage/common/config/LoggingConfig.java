package com.hj.manage.common.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * 日志配置类
 */
@Configuration
public class LoggingConfig {

    private static final Logger logger = LoggerFactory.getLogger(LoggingConfig.class);

    @PostConstruct
    public void init() {
        logger.info("=== 工装夹具管理系统启动 ===");
        logger.info("日志配置初始化完成");
    }

    /**
     * 获取指定类的Logger
     */
    public static Logger getLogger(Class<?> clazz) {
        return LoggerFactory.getLogger(clazz);
    }

    /**
     * 获取指定名称的Logger
     */
    public static Logger getLogger(String name) {
        return LoggerFactory.getLogger(name);
    }

    /**
     * 记录方法执行时间
     */
    public static void logExecutionTime(Logger logger, String methodName, long startTime) {
        long executionTime = System.currentTimeMillis() - startTime;
        if (executionTime > 1000) {
            logger.warn("方法 {} 执行时间较长: {}ms", methodName, executionTime);
        } else {
            logger.debug("方法 {} 执行时间: {}ms", methodName, executionTime);
        }
    }

    /**
     * 记录API调用信息
     */
    public static void logApiCall(Logger logger, String method, String uri, String params) {
        logger.info("API调用: {} {} 参数: {}", method, uri, params);
    }

    /**
     * 记录API响应信息
     */
    public static void logApiResponse(Logger logger, String method, String uri, int statusCode, long executionTime) {
        logger.info("API响应: {} {} 状态码: {} 耗时: {}ms", method, uri, statusCode, executionTime);
    }

    /**
     * 记录业务操作日志
     */
    public static void logBusinessOperation(Logger logger, String operation, String user, String details) {
        logger.info("业务操作: {} 用户: {} 详情: {}", operation, user, details);
    }

    /**
     * 记录安全相关日志
     */
    public static void logSecurityEvent(Logger logger, String event, String user, String ip, String details) {
        logger.warn("安全事件: {} 用户: {} IP: {} 详情: {}", event, user, ip, details);
    }
}
