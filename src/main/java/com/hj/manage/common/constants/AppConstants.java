package com.hj.manage.common.constants;

/**
 * 应用常量类
 * 统一管理系统中使用的常量
 */
public class AppConstants {

    /**
     * 系统信息
     */
    public static final String APP_NAME = "工装夹具管理系统";
    public static final String DEFAULT_VERSION = "1.0.0";
    public static final String APP_FILE_PATH = "app/gzjj-app.apk";

    /**
     * 用户角色常量
     */
    public static class UserRole {
        public static final String ADMIN = "管理人员";
        public static final String NORMAL_USER = "普通用户";
        public static final String WAREHOUSE_STAFF = "仓库人员";
        public static final String PRODUCTION_STAFF = "生产人员";
    }

    /**
     * 工具状态常量
     */
    public static class ToolStatus {
        public static final String IN_STOCK = "库存";
        public static final String IN_USE = "使用中";
        public static final String UNDER_REPAIR = "维修中";
        public static final String SCRAPPED = "报废";
    }

    /**
     * 使用记录状态常量
     */
    public static class UsageStatus {
        public static final String IN_PROGRESS = "进行中";
        public static final String COMPLETED = "已完成";
    }

    /**
     * 拆解申请状态常量
     */
    public static class DisassembleStatus {
        public static final String PENDING = "待审核";
        public static final String APPROVED = "已通过";
        public static final String REJECTED = "已拒绝";
        public static final String COMPLETED = "已完成";
    }

    /**
     * 响应状态码常量
     */
    public static class ResponseCode {
        public static final int SUCCESS = 200;
        public static final int BAD_REQUEST = 400;
        public static final int UNAUTHORIZED = 401;
        public static final int FORBIDDEN = 403;
        public static final int NOT_FOUND = 404;
        public static final int INTERNAL_SERVER_ERROR = 500;
    }

    /**
     * 响应消息常量
     */
    public static class ResponseMessage {
        public static final String SUCCESS = "操作成功";
        public static final String FAILED = "操作失败";
        public static final String LOGIN_SUCCESS = "登录成功";
        public static final String LOGIN_FAILED = "登录失败";
        public static final String INVALID_PARAMS = "参数错误";
        public static final String UNAUTHORIZED_ACCESS = "未授权访问";
        public static final String RESOURCE_NOT_FOUND = "资源不存在";
        public static final String INTERNAL_ERROR = "系统内部错误";
    }

    /**
     * 数据库相关常量
     */
    public static class Database {
        public static final int DEFAULT_PAGE_SIZE = 10;
        public static final int MAX_PAGE_SIZE = 100;
        public static final String DEFAULT_SORT_FIELD = "id";
        public static final String DEFAULT_SORT_DIRECTION = "DESC";
    }

    /**
     * 文件相关常量
     */
    public static class File {
        public static final String EXCEL_CONTENT_TYPE = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
        public static final String APK_CONTENT_TYPE = "application/vnd.android.package-archive";
        public static final long MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
        public static final String[] ALLOWED_IMAGE_TYPES = {"jpg", "jpeg", "png", "gif"};
    }

    /**
     * 缓存相关常量
     */
    public static class Cache {
        public static final String USER_CACHE_PREFIX = "user:";
        public static final String TOOL_CACHE_PREFIX = "tool:";
        public static final String VERSION_CACHE_PREFIX = "version:";
        public static final int DEFAULT_CACHE_EXPIRE_SECONDS = 3600; // 1小时
    }

    /**
     * 日期时间格式常量
     */
    public static class DateFormat {
        public static final String STANDARD_DATE_TIME = "yyyy-MM-dd HH:mm:ss";
        public static final String STANDARD_DATE = "yyyy-MM-dd";
        public static final String STANDARD_TIME = "HH:mm:ss";
        public static final String COMPACT_DATE_TIME = "yyyyMMddHHmmss";
    }

    /**
     * 正则表达式常量
     */
    public static class Regex {
        public static final String PHONE_NUMBER = "^1[3-9]\\d{9}$";
        public static final String VERSION_NUMBER = "^\\d+\\.\\d+\\.\\d+$";
        public static final String TOOL_CODE = "^[A-Z0-9]{6,20}$";
        public static final String USERNAME = "^[a-zA-Z0-9_]{3,20}$";
    }

    /**
     * 私有构造函数，防止实例化
     */
    private AppConstants() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
}
