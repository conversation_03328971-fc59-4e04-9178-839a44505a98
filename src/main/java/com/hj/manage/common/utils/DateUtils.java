package com.hj.manage.common.utils;

import com.hj.manage.common.constants.AppConstants;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Date;

/**
 * 日期时间工具类
 */
public class DateUtils {

    private static final DateTimeFormatter STANDARD_DATE_TIME_FORMATTER = 
            DateTimeFormatter.ofPattern(AppConstants.DateFormat.STANDARD_DATE_TIME);
    
    private static final DateTimeFormatter STANDARD_DATE_FORMATTER = 
            DateTimeFormatter.ofPattern(AppConstants.DateFormat.STANDARD_DATE);
    
    private static final DateTimeFormatter COMPACT_DATE_TIME_FORMATTER = 
            DateTimeFormatter.ofPattern(AppConstants.DateFormat.COMPACT_DATE_TIME);

    /**
     * 格式化LocalDateTime为标准格式字符串
     */
    public static String formatDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return dateTime.format(STANDARD_DATE_TIME_FORMATTER);
    }

    /**
     * 格式化LocalDate为标准格式字符串
     */
    public static String formatDate(LocalDate date) {
        if (date == null) {
            return null;
        }
        return date.format(STANDARD_DATE_FORMATTER);
    }

    /**
     * 格式化LocalDateTime为紧凑格式字符串
     */
    public static String formatCompactDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return dateTime.format(COMPACT_DATE_TIME_FORMATTER);
    }

    /**
     * 解析标准格式日期时间字符串
     */
    public static LocalDateTime parseDateTime(String dateTimeStr) {
        if (StringUtils.isEmpty(dateTimeStr)) {
            return null;
        }
        try {
            return LocalDateTime.parse(dateTimeStr, STANDARD_DATE_TIME_FORMATTER);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("日期时间格式错误: " + dateTimeStr, e);
        }
    }

    /**
     * 解析标准格式日期字符串
     */
    public static LocalDate parseDate(String dateStr) {
        if (StringUtils.isEmpty(dateStr)) {
            return null;
        }
        try {
            return LocalDate.parse(dateStr, STANDARD_DATE_FORMATTER);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("日期格式错误: " + dateStr, e);
        }
    }

    /**
     * 获取当前日期时间字符串
     */
    public static String getCurrentDateTime() {
        return formatDateTime(LocalDateTime.now());
    }

    /**
     * 获取当前日期字符串
     */
    public static String getCurrentDate() {
        return formatDate(LocalDate.now());
    }

    /**
     * 获取当前紧凑格式日期时间字符串
     */
    public static String getCurrentCompactDateTime() {
        return formatCompactDateTime(LocalDateTime.now());
    }

    /**
     * 检查日期是否在指定范围内
     */
    public static boolean isDateInRange(LocalDate date, LocalDate startDate, LocalDate endDate) {
        if (date == null) {
            return false;
        }
        if (startDate != null && date.isBefore(startDate)) {
            return false;
        }
        if (endDate != null && date.isAfter(endDate)) {
            return false;
        }
        return true;
    }

    /**
     * 检查日期时间是否在指定范围内
     */
    public static boolean isDateTimeInRange(LocalDateTime dateTime, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        if (dateTime == null) {
            return false;
        }
        if (startDateTime != null && dateTime.isBefore(startDateTime)) {
            return false;
        }
        if (endDateTime != null && dateTime.isAfter(endDateTime)) {
            return false;
        }
        return true;
    }

    /**
     * 计算两个日期之间的天数差
     */
    public static long daysBetween(LocalDate startDate, LocalDate endDate) {
        if (startDate == null || endDate == null) {
            return 0;
        }
        return java.time.temporal.ChronoUnit.DAYS.between(startDate, endDate);
    }

    /**
     * 私有构造函数，防止实例化
     */
    private DateUtils() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
}
