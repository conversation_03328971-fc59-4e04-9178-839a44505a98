package com.hj.manage.common.utils;

import com.hj.manage.common.constants.AppConstants;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

/**
 * 分页工具类
 */
public class PageUtils {

    /**
     * 创建分页对象
     */
    public static Pageable createPageable(Integer page, Integer size) {
        return createPageable(page, size, null, null);
    }

    /**
     * 创建分页对象（带排序）
     */
    public static Pageable createPageable(Integer page, Integer size, String sortField, String sortDirection) {
        // 处理默认值
        if (page == null || page < 0) {
            page = 0;
        }
        if (size == null || size <= 0) {
            size = AppConstants.Database.DEFAULT_PAGE_SIZE;
        }
        if (size > AppConstants.Database.MAX_PAGE_SIZE) {
            size = AppConstants.Database.MAX_PAGE_SIZE;
        }

        // 创建排序对象
        Sort sort = createSort(sortField, sortDirection);
        
        if (sort != null) {
            return PageRequest.of(page, size, sort);
        } else {
            return PageRequest.of(page, size);
        }
    }

    /**
     * 创建排序对象
     */
    public static Sort createSort(String sortField, String sortDirection) {
        if (StringUtils.isEmpty(sortField)) {
            sortField = AppConstants.Database.DEFAULT_SORT_FIELD;
        }
        
        if (StringUtils.isEmpty(sortDirection)) {
            sortDirection = AppConstants.Database.DEFAULT_SORT_DIRECTION;
        }

        Sort.Direction direction;
        try {
            direction = Sort.Direction.fromString(sortDirection);
        } catch (IllegalArgumentException e) {
            direction = Sort.Direction.DESC;
        }

        return Sort.by(direction, sortField);
    }

    /**
     * 验证分页参数
     */
    public static void validatePageParams(Integer page, Integer size) {
        if (page != null && page < 0) {
            throw new IllegalArgumentException("页码不能小于0");
        }
        if (size != null && size <= 0) {
            throw new IllegalArgumentException("页面大小必须大于0");
        }
        if (size != null && size > AppConstants.Database.MAX_PAGE_SIZE) {
            throw new IllegalArgumentException("页面大小不能超过" + AppConstants.Database.MAX_PAGE_SIZE);
        }
    }

    /**
     * 计算总页数
     */
    public static int calculateTotalPages(long totalElements, int pageSize) {
        if (pageSize <= 0) {
            return 0;
        }
        return (int) Math.ceil((double) totalElements / pageSize);
    }

    /**
     * 计算偏移量
     */
    public static long calculateOffset(int page, int size) {
        return (long) page * size;
    }

    /**
     * 检查是否为第一页
     */
    public static boolean isFirstPage(int page) {
        return page == 0;
    }

    /**
     * 检查是否为最后一页
     */
    public static boolean isLastPage(int page, int totalPages) {
        return page >= totalPages - 1;
    }

    /**
     * 私有构造函数，防止实例化
     */
    private PageUtils() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
}
