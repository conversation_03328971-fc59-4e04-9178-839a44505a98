package com.hj.manage.common.utils;

import com.hj.manage.common.constants.AppConstants;
import com.hj.manage.common.enums.UserRoleEnum;
import com.hj.manage.common.enums.ToolStatusEnum;

import java.math.BigDecimal;
import java.util.regex.Pattern;

/**
 * 验证工具类
 */
public class ValidationUtils {

    /**
     * 验证用户名
     */
    public static boolean isValidUsername(String username) {
        if (StringUtils.isEmpty(username)) {
            return false;
        }
        return Pattern.matches(AppConstants.Regex.USERNAME, username);
    }

    /**
     * 验证手机号
     */
    public static boolean isValidPhoneNumber(String phoneNumber) {
        if (StringUtils.isEmpty(phoneNumber)) {
            return false;
        }
        return Pattern.matches(AppConstants.Regex.PHONE_NUMBER, phoneNumber);
    }

    /**
     * 验证版本号
     */
    public static boolean isValidVersionNumber(String versionNumber) {
        if (StringUtils.isEmpty(versionNumber)) {
            return false;
        }
        return Pattern.matches(AppConstants.Regex.VERSION_NUMBER, versionNumber);
    }

    /**
     * 验证工具编号
     */
    public static boolean isValidToolCode(String toolCode) {
        if (StringUtils.isEmpty(toolCode)) {
            return false;
        }
        return Pattern.matches(AppConstants.Regex.TOOL_CODE, toolCode);
    }

    /**
     * 验证用户角色
     */
    public static boolean isValidUserRole(String role) {
        if (StringUtils.isEmpty(role)) {
            return false;
        }
        try {
            UserRoleEnum.fromRoleName(role);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    /**
     * 验证工具状态
     */
    public static boolean isValidToolStatus(String status) {
        if (StringUtils.isEmpty(status)) {
            return false;
        }
        try {
            ToolStatusEnum.fromStatusName(status);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    /**
     * 验证价格
     */
    public static boolean isValidPrice(BigDecimal price) {
        return price != null && price.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * 验证ID
     */
    public static boolean isValidId(Integer id) {
        return id != null && id > 0;
    }

    /**
     * 验证分页参数
     */
    public static boolean isValidPageParams(Integer page, Integer size) {
        if (page != null && page < 0) {
            return false;
        }
        if (size != null && (size <= 0 || size > AppConstants.Database.MAX_PAGE_SIZE)) {
            return false;
        }
        return true;
    }

    /**
     * 验证邮箱地址
     */
    public static boolean isValidEmail(String email) {
        if (StringUtils.isEmpty(email)) {
            return false;
        }
        String emailRegex = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
        return Pattern.matches(emailRegex, email);
    }

    /**
     * 验证密码强度
     */
    public static boolean isValidPassword(String password) {
        if (StringUtils.isEmpty(password)) {
            return false;
        }
        // 密码长度至少6位
        if (password.length() < 6) {
            return false;
        }
        // 可以添加更多密码强度验证规则
        return true;
    }

    /**
     * 验证文件大小
     */
    public static boolean isValidFileSize(long fileSize) {
        return fileSize > 0 && fileSize <= AppConstants.File.MAX_FILE_SIZE;
    }

    /**
     * 验证文件类型
     */
    public static boolean isValidImageType(String fileName) {
        if (StringUtils.isEmpty(fileName)) {
            return false;
        }
        String extension = getFileExtension(fileName).toLowerCase();
        for (String allowedType : AppConstants.File.ALLOWED_IMAGE_TYPES) {
            if (allowedType.equals(extension)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取文件扩展名
     */
    public static String getFileExtension(String fileName) {
        if (StringUtils.isEmpty(fileName)) {
            return "";
        }
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == fileName.length() - 1) {
            return "";
        }
        return fileName.substring(lastDotIndex + 1);
    }

    /**
     * 验证字符串长度
     */
    public static boolean isValidLength(String str, int minLength, int maxLength) {
        if (str == null) {
            return minLength == 0;
        }
        int length = str.length();
        return length >= minLength && length <= maxLength;
    }

    /**
     * 验证数值范围
     */
    public static boolean isInRange(Integer value, int min, int max) {
        return value != null && value >= min && value <= max;
    }

    /**
     * 验证数值范围
     */
    public static boolean isInRange(Long value, long min, long max) {
        return value != null && value >= min && value <= max;
    }

    /**
     * 私有构造函数，防止实例化
     */
    private ValidationUtils() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
}
