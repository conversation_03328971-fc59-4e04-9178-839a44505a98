package com.hj.manage.common.utils;

import java.util.Collection;
import java.util.regex.Pattern;

/**
 * 字符串工具类
 */
public class StringUtils {

    /**
     * 检查字符串是否为空
     */
    public static boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }

    /**
     * 检查字符串是否不为空
     */
    public static boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }

    /**
     * 检查字符串是否为空白
     */
    public static boolean isBlank(String str) {
        return str == null || str.trim().isEmpty();
    }

    /**
     * 检查字符串是否不为空白
     */
    public static boolean isNotBlank(String str) {
        return !isBlank(str);
    }

    /**
     * 安全的字符串trim操作
     */
    public static String safeTrim(String str) {
        return str == null ? null : str.trim();
    }

    /**
     * 获取字符串的安全长度
     */
    public static int safeLength(String str) {
        return str == null ? 0 : str.length();
    }

    /**
     * 字符串默认值处理
     */
    public static String defaultIfEmpty(String str, String defaultValue) {
        return isEmpty(str) ? defaultValue : str;
    }

    /**
     * 字符串默认值处理（空白）
     */
    public static String defaultIfBlank(String str, String defaultValue) {
        return isBlank(str) ? defaultValue : str;
    }

    /**
     * 检查字符串是否匹配正则表达式
     */
    public static boolean matches(String str, String regex) {
        if (isEmpty(str) || isEmpty(regex)) {
            return false;
        }
        return Pattern.matches(regex, str);
    }

    /**
     * 检查是否为有效的手机号
     */
    public static boolean isValidPhoneNumber(String phoneNumber) {
        return matches(phoneNumber, "^1[3-9]\\d{9}$");
    }

    /**
     * 检查是否为有效的版本号
     */
    public static boolean isValidVersionNumber(String versionNumber) {
        return matches(versionNumber, "^\\d+\\.\\d+\\.\\d+$");
    }

    /**
     * 检查是否为有效的工具编号
     */
    public static boolean isValidToolCode(String toolCode) {
        return matches(toolCode, "^[A-Z0-9]{6,20}$");
    }

    /**
     * 检查是否为有效的用户名
     */
    public static boolean isValidUsername(String username) {
        return matches(username, "^[a-zA-Z0-9_]{3,20}$");
    }

    /**
     * 将字符串首字母大写
     */
    public static String capitalize(String str) {
        if (isEmpty(str)) {
            return str;
        }
        return str.substring(0, 1).toUpperCase() + str.substring(1);
    }

    /**
     * 将字符串首字母小写
     */
    public static String uncapitalize(String str) {
        if (isEmpty(str)) {
            return str;
        }
        return str.substring(0, 1).toLowerCase() + str.substring(1);
    }

    /**
     * 连接字符串数组
     */
    public static String join(String[] array, String separator) {
        if (array == null || array.length == 0) {
            return "";
        }
        if (separator == null) {
            separator = "";
        }
        
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < array.length; i++) {
            if (i > 0) {
                sb.append(separator);
            }
            sb.append(array[i]);
        }
        return sb.toString();
    }

    /**
     * 连接集合
     */
    public static String join(Collection<String> collection, String separator) {
        if (collection == null || collection.isEmpty()) {
            return "";
        }
        return join(collection.toArray(new String[0]), separator);
    }

    /**
     * 截取字符串
     */
    public static String substring(String str, int start, int end) {
        if (str == null) {
            return null;
        }
        if (start < 0) {
            start = 0;
        }
        if (end > str.length()) {
            end = str.length();
        }
        if (start > end) {
            return "";
        }
        return str.substring(start, end);
    }

    /**
     * 安全的字符串比较
     */
    public static boolean equals(String str1, String str2) {
        if (str1 == null && str2 == null) {
            return true;
        }
        if (str1 == null || str2 == null) {
            return false;
        }
        return str1.equals(str2);
    }

    /**
     * 忽略大小写的字符串比较
     */
    public static boolean equalsIgnoreCase(String str1, String str2) {
        if (str1 == null && str2 == null) {
            return true;
        }
        if (str1 == null || str2 == null) {
            return false;
        }
        return str1.equalsIgnoreCase(str2);
    }

    /**
     * 私有构造函数，防止实例化
     */
    private StringUtils() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
}
