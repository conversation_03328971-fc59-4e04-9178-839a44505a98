package com.hj.manage.common.enums;

/**
 * 用户角色枚举
 */
public enum UserRoleEnum {
    
    ADMIN("管理人员", "系统管理员，拥有所有权限"),
    NORMAL_USER("普通用户", "普通用户，基础权限"),
    WAREHOUSE_STAFF("仓库人员", "仓库管理人员"),
    PRODUCTION_STAFF("生产人员", "生产线工作人员");

    private final String roleName;
    private final String description;

    UserRoleEnum(String roleName, String description) {
        this.roleName = roleName;
        this.description = description;
    }

    public String getRoleName() {
        return roleName;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据角色名称获取枚举
     */
    public static UserRoleEnum fromRoleName(String roleName) {
        for (UserRoleEnum role : UserRoleEnum.values()) {
            if (role.getRoleName().equals(roleName)) {
                return role;
            }
        }
        throw new IllegalArgumentException("未知的用户角色: " + roleName);
    }

    /**
     * 检查是否为管理员角色
     */
    public boolean isAdmin() {
        return this == ADMIN;
    }

    /**
     * 检查是否有管理权限
     */
    public boolean hasManagementPermission() {
        return this == ADMIN;
    }

    /**
     * 检查是否有版本管理权限
     */
    public boolean hasVersionManagementPermission() {
        return this == ADMIN;
    }

    /**
     * 获取所有角色名称
     */
    public static String[] getAllRoleNames() {
        UserRoleEnum[] values = UserRoleEnum.values();
        String[] roleNames = new String[values.length];
        for (int i = 0; i < values.length; i++) {
            roleNames[i] = values[i].getRoleName();
        }
        return roleNames;
    }
}
