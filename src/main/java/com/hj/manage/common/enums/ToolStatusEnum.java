package com.hj.manage.common.enums;

/**
 * 工具状态枚举
 */
public enum ToolStatusEnum {
    
    IN_STOCK("库存", "工具在库存中，可以使用"),
    IN_USE("使用中", "工具正在被使用"),
    UNDER_REPAIR("维修中", "工具正在维修"),
    SCRAPPED("报废", "工具已报废，不可使用");

    private final String statusName;
    private final String description;

    ToolStatusEnum(String statusName, String description) {
        this.statusName = statusName;
        this.description = description;
    }

    public String getStatusName() {
        return statusName;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据状态名称获取枚举
     */
    public static ToolStatusEnum fromStatusName(String statusName) {
        for (ToolStatusEnum status : ToolStatusEnum.values()) {
            if (status.getStatusName().equals(statusName)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的工具状态: " + statusName);
    }

    /**
     * 检查是否可以使用
     */
    public boolean isAvailable() {
        return this == IN_STOCK;
    }

    /**
     * 检查是否正在使用
     */
    public boolean isInUse() {
        return this == IN_USE;
    }

    /**
     * 检查是否已报废
     */
    public boolean isScrapped() {
        return this == SCRAPPED;
    }

    /**
     * 获取所有状态名称
     */
    public static String[] getAllStatusNames() {
        ToolStatusEnum[] values = ToolStatusEnum.values();
        String[] statusNames = new String[values.length];
        for (int i = 0; i < values.length; i++) {
            statusNames[i] = values[i].getStatusName();
        }
        return statusNames;
    }

    /**
     * 获取可用状态列表
     */
    public static ToolStatusEnum[] getAvailableStatuses() {
        return new ToolStatusEnum[]{IN_STOCK, IN_USE, UNDER_REPAIR};
    }
}
