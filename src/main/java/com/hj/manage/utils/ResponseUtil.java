package com.hj.manage.utils;

import com.hj.manage.dto.ApiResponse;
import com.hj.manage.common.constants.AppConstants;

import java.util.Map;
import java.util.HashMap;

/**
 * 响应工具类
 * @deprecated 建议使用 {@link ApiResponse} 替代
 */
@Deprecated
public class ResponseUtil {

    public static Map<String, Object> success(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", message);
        response.put("code", AppConstants.ResponseCode.SUCCESS);
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }

    public static Map<String, Object> success(String message, Object data) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", message);
        response.put("data", data);
        response.put("code", AppConstants.ResponseCode.SUCCESS);
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }

    public static Map<String, Object> warning(String message, Object data) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("warning", true);
        response.put("message", message);
        response.put("data", data);
        response.put("code", AppConstants.ResponseCode.BAD_REQUEST);
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }

    public static Map<String, Object> error(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("error", true);
        response.put("message", message);
        response.put("code", AppConstants.ResponseCode.BAD_REQUEST);
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }

    /**
     * 转换为新的ApiResponse格式
     */
    public static <T> ApiResponse<T> toApiResponse(Map<String, Object> oldResponse) {
        boolean success = (Boolean) oldResponse.getOrDefault("success", false);
        String message = (String) oldResponse.getOrDefault("message", "");
        int code = (Integer) oldResponse.getOrDefault("code", AppConstants.ResponseCode.BAD_REQUEST);
        @SuppressWarnings("unchecked")
        T data = (T) oldResponse.get("data");

        if (success) {
            return ApiResponse.success(data, message);
        } else {
            return ApiResponse.error(code, message);
        }
    }
}