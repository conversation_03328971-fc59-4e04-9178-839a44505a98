package com.hj.manage.dto.usage;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * 创建使用记录请求DTO
 */
@Data
public class CreateUsageRecordRequest {

    /**
     * 工具编号
     */
    @NotBlank(message = "工具编号不能为空")
    private String toolCode;

    /**
     * 机器编号
     */
    @NotBlank(message = "机器编号不能为空")
    private String machineCode;

    /**
     * 任务编号
     */
    @NotBlank(message = "任务编号不能为空")
    private String taskCode;

    /**
     * 开始时间（可选，默认为当前时间）
     */
    private LocalDateTime startTime;

    /**
     * 结束时间（可选）
     */
    private LocalDateTime endTime;

    /**
     * 默认构造函数
     */
    public CreateUsageRecordRequest() {}

    /**
     * 构造函数
     */
    public CreateUsageRecordRequest(String toolCode, String machineCode, String taskCode,
                                  LocalDateTime startTime, LocalDateTime endTime) {
        this.toolCode = toolCode;
        this.machineCode = machineCode;
        this.taskCode = taskCode;
        this.startTime = startTime;
        this.endTime = endTime;
    }
}
