package com.hj.manage.dto.usage;

import lombok.Data;
import com.hj.manage.domain.UsageRecord;
import java.time.LocalDateTime;

/**
 * 使用记录响应DTO
 */
@Data
public class UsageRecordResponse {

    /**
     * 记录ID
     */
    private Integer id;

    /**
     * 工具编号
     */
    private String toolCode;

    /**
     * 机器编号
     */
    private String machineCode;

    /**
     * 任务编号
     */
    private String taskCode;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 使用状态（进行中/已完成）
     */
    private String status;

    /**
     * 默认构造函数
     */
    public UsageRecordResponse() {}

    /**
     * 从UsageRecord实体转换的构造函数
     */
    public UsageRecordResponse(UsageRecord record) {
        if (record != null) {
            this.id = record.getId();
            this.toolCode = record.getToolCode();
            this.machineCode = record.getMachineCode();
            this.taskCode = record.getTaskCode();
            this.startTime = record.getStartTime();
            this.endTime = record.getEndTime();
            this.status = record.getEndTime() == null ? "进行中" : "已完成";
        }
    }

    /**
     * 从UsageRecord实体转换为UsageRecordResponse
     */
    public static UsageRecordResponse fromUsageRecord(UsageRecord record) {
        return new UsageRecordResponse(record);
    }
}
