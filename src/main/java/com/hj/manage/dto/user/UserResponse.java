package com.hj.manage.dto.user;

import lombok.Data;
import com.hj.manage.domain.User;

/**
 * 用户响应DTO
 * 用于返回用户信息，包含密码字段用于管理界面编辑
 */
@Data
public class UserResponse {

    /**
     * 用户ID
     */
    private Integer id;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 角色
     */
    private String role;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 默认构造函数
     */
    public UserResponse() {}

    /**
     * 从User实体转换的构造函数
     */
    public UserResponse(User user) {
        if (user != null) {
            this.id = user.getId();
            this.username = user.getUsername();
            this.password = user.getPassword();
            this.role = user.getRole();
            this.phone = user.getPhone();
        }
    }

    /**
     * 构造函数
     */
    public UserResponse(Integer id, String username, String password, String role, String phone) {
        this.id = id;
        this.username = username;
        this.password = password;
        this.role = role;
        this.phone = phone;
    }

    /**
     * 从User实体转换为UserResponse
     */
    public static UserResponse fromUser(User user) {
        return new UserResponse(user);
    }
}
