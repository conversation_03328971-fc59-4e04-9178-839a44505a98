package com.hj.manage.dto.user;

import lombok.Data;
import javax.validation.constraints.NotBlank;

/**
 * 用户登录请求DTO
 */
@Data
public class LoginRequest {

    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空")
    private String username;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    private String password;

    /**
     * 默认构造函数
     */
    public LoginRequest() {}

    /**
     * 构造函数
     */
    public LoginRequest(String username, String password) {
        this.username = username;
        this.password = password;
    }
}
