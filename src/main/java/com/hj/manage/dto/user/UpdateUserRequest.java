package com.hj.manage.dto.user;

import lombok.Data;
import javax.validation.constraints.Pattern;

/**
 * 更新用户请求DTO
 */
@Data
public class UpdateUserRequest {

    /**
     * 用户名（可选）
     */
    private String username;

    /**
     * 密码（可选）
     */
    private String password;

    /**
     * 角色（可选）
     */
    @Pattern(regexp = "^(管理人员|仓库人员|生产人员)$", message = "角色必须是：管理人员、仓库人员、生产人员之一")
    private String role;

    /**
     * 手机号（可选）
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    /**
     * 默认构造函数
     */
    public UpdateUserRequest() {}

    /**
     * 构造函数
     */
    public UpdateUserRequest(String username, String password, String role, String phone) {
        this.username = username;
        this.password = password;
        this.role = role;
        this.phone = phone;
    }
}
