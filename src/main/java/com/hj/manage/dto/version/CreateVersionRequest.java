package com.hj.manage.dto.version;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 创建版本请求DTO
 */
@Data
public class CreateVersionRequest {

    /**
     * 版本号
     */
    @NotBlank(message = "版本号不能为空")
    @Pattern(regexp = "^\\d+\\.\\d+\\.\\d+$", message = "版本号格式应为 x.x.x")
    private String versionName;

    /**
     * 下载链接
     */
    private String downloadUrl;

    /**
     * 更新日志
     */
    @NotBlank(message = "更新日志不能为空")
    private String updateLog;

    /**
     * 默认构造函数
     */
    public CreateVersionRequest() {}

    /**
     * 构造函数
     */
    public CreateVersionRequest(String versionName, String downloadUrl, String updateLog) {
        this.versionName = versionName;
        this.downloadUrl = downloadUrl;
        this.updateLog = updateLog;
    }
}
