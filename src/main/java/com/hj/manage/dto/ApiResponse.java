package com.hj.manage.dto;

import lombok.Data;

/**
 * 通用API响应DTO
 * @param <T> 数据类型
 */
@Data
public class ApiResponse<T> {
    
    /**
     * 响应状态码
     */
    private int code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 请求是否成功
     */
    private boolean success;
    
    /**
     * 时间戳
     */
    private long timestamp;

    /**
     * 默认构造函数
     */
    public ApiResponse() {
        this.timestamp = System.currentTimeMillis();
    }

    /**
     * 成功响应构造函数
     */
    public ApiResponse(T data) {
        this();
        this.code = 200;
        this.success = true;
        this.message = "操作成功";
        this.data = data;
    }

    /**
     * 成功响应构造函数（带消息）
     */
    public ApiResponse(T data, String message) {
        this();
        this.code = 200;
        this.success = true;
        this.message = message;
        this.data = data;
    }

    /**
     * 错误响应构造函数
     */
    public ApiResponse(int code, String message) {
        this();
        this.code = code;
        this.success = false;
        this.message = message;
        this.data = null;
    }

    /**
     * 创建成功响应
     */
    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(data);
    }

    /**
     * 创建成功响应（带消息）
     */
    public static <T> ApiResponse<T> success(T data, String message) {
        return new ApiResponse<>(data, message);
    }

    /**
     * 创建成功响应（无数据）
     */
    public static <T> ApiResponse<T> success(String message) {
        return new ApiResponse<>(null, message);
    }

    /**
     * 创建错误响应
     */
    public static <T> ApiResponse<T> error(String message) {
        return new ApiResponse<>(400, message);
    }

    /**
     * 创建错误响应（带状态码）
     */
    public static <T> ApiResponse<T> error(int code, String message) {
        return new ApiResponse<>(code, message);
    }

    /**
     * 创建参数错误响应
     */
    public static <T> ApiResponse<T> badRequest(String message) {
        return new ApiResponse<>(400, message);
    }

    /**
     * 创建未找到响应
     */
    public static <T> ApiResponse<T> notFound(String message) {
        return new ApiResponse<>(404, message);
    }

    /**
     * 创建服务器错误响应
     */
    public static <T> ApiResponse<T> serverError(String message) {
        return new ApiResponse<>(500, message);
    }

    /**
     * 创建未授权响应
     */
    public static <T> ApiResponse<T> unauthorized(String message) {
        return new ApiResponse<>(401, message);
    }

    /**
     * 创建禁止访问响应
     */
    public static <T> ApiResponse<T> forbidden(String message) {
        return new ApiResponse<>(403, message);
    }
}
