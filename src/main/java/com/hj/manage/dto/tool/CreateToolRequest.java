package com.hj.manage.dto.tool;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.DecimalMin;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 创建工具请求DTO
 */
@Data
public class CreateToolRequest {

    /**
     * 工具编号
     */
    @NotBlank(message = "工具编号不能为空")
    private String toolCode;

    /**
     * 工具名称
     */
    @NotBlank(message = "工具名称不能为空")
    private String name;

    /**
     * 型号
     */
    private String model;

    /**
     * 规格
     */
    private String specification;

    /**
     * 供应商
     */
    private String supplier;

    /**
     * 采购日期
     */
    private Date purchaseDate;

    /**
     * 价格
     */
    @DecimalMin(value = "0.0", inclusive = false, message = "价格必须大于0")
    private BigDecimal price;

    /**
     * 状态（可选，默认为"库存"）
     */
    private String status = "库存";

    /**
     * 默认构造函数
     */
    public CreateToolRequest() {}

    /**
     * 构造函数
     */
    public CreateToolRequest(String toolCode, String name, String model, String specification,
                           String supplier, Date purchaseDate, BigDecimal price) {
        this.toolCode = toolCode;
        this.name = name;
        this.model = model;
        this.specification = specification;
        this.supplier = supplier;
        this.purchaseDate = purchaseDate;
        this.price = price;
    }
}
