package com.hj.manage.dto.tool;

import lombok.Data;
import com.hj.manage.domain.Tool;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 工具响应DTO
 */
@Data
public class ToolResponse {

    /**
     * 工具ID
     */
    private Integer id;

    /**
     * 工具编号
     */
    private String toolCode;

    /**
     * 工具名称
     */
    private String name;

    /**
     * 型号
     */
    private String model;

    /**
     * 规格
     */
    private String specification;

    /**
     * 供应商
     */
    private String supplier;

    /**
     * 采购日期
     */
    private Date purchaseDate;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 状态
     */
    private String status;

    /**
     * 默认构造函数
     */
    public ToolResponse() {}

    /**
     * 从Tool实体转换的构造函数
     */
    public ToolResponse(Tool tool) {
        if (tool != null) {
            this.id = tool.getId();
            this.toolCode = tool.getToolCode();
            this.name = tool.getName();
            this.model = tool.getModel();
            this.specification = tool.getSpecification();
            this.supplier = tool.getSupplier();
            this.purchaseDate = tool.getPurchaseDate();
            this.price = tool.getPrice();
            this.status = tool.getStatus();
        }
    }

    /**
     * 从Tool实体转换为ToolResponse
     */
    public static ToolResponse fromTool(Tool tool) {
        return new ToolResponse(tool);
    }
}
