package com.hj.manage.dto.tool;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 更新工具状态请求DTO
 */
@Data
public class UpdateToolStatusRequest {

    /**
     * 工具编号
     */
    @NotBlank(message = "工具编号不能为空")
    private String toolCode;

    /**
     * 新状态
     */
    @NotBlank(message = "状态不能为空")
    @Pattern(regexp = "^(库存|使用中|维修中|报废)$", message = "状态必须是：库存、使用中、维修中、报废之一")
    private String newStatus;

    /**
     * 默认构造函数
     */
    public UpdateToolStatusRequest() {}

    /**
     * 构造函数
     */
    public UpdateToolStatusRequest(String toolCode, String newStatus) {
        this.toolCode = toolCode;
        this.newStatus = newStatus;
    }
}
