package com.hj.manage.dto;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 版本检查结果DTO
 * 用于返回版本检查的结果信息
 */
@Data
public class VersionCheckResult {

    /**
     * 是否有新版本
     */
    private boolean hasNewVersion;

    /**
     * 最新版本号
     */
    private String latestVersionName;

    /**
     * 最新版本代码
     */
    private Integer latestVersionCode;

    /**
     * 下载链接
     */
    private String downloadUrl;

    /**
     * 更新日志
     */
    private String updateLog;

    /**
     * 版本创建时间
     */
    private LocalDateTime createTime;

    /**
     * 当前版本号
     */
    private String currentVersionName;

    /**
     * 当前版本代码
     */
    private Integer currentVersionCode;

    /**
     * 检查消息
     */
    private String message;

    /**
     * 默认构造函数
     */
    public VersionCheckResult() {}

    /**
     * 无新版本的构造函数
     */
    public VersionCheckResult(Integer currentVersionCode, String currentVersionName) {
        this.hasNewVersion = false;
        this.currentVersionCode = currentVersionCode;
        this.currentVersionName = currentVersionName;
        this.message = "当前已是最新版本";
    }

    /**
     * 有新版本的构造函数
     */
    public VersionCheckResult(Integer currentVersionCode, String currentVersionName,
                            String latestVersionName, Integer latestVersionCode,
                            String downloadUrl, String updateLog, LocalDateTime createTime) {
        this.hasNewVersion = true;
        this.currentVersionCode = currentVersionCode;
        this.currentVersionName = currentVersionName;
        this.latestVersionName = latestVersionName;
        this.latestVersionCode = latestVersionCode;
        this.downloadUrl = downloadUrl;
        this.updateLog = updateLog;
        this.createTime = createTime;
        this.message = "发现新版本：" + latestVersionName;
    }

    /**
     * 创建无新版本的结果
     */
    public static VersionCheckResult noNewVersion(Integer currentVersionCode, String currentVersionName) {
        return new VersionCheckResult(currentVersionCode, currentVersionName);
    }

    /**
     * 创建有新版本的结果
     */
    public static VersionCheckResult hasNewVersion(Integer currentVersionCode, String currentVersionName,
                                                 String latestVersionName, Integer latestVersionCode,
                                                 String downloadUrl, String updateLog, LocalDateTime createTime) {
        return new VersionCheckResult(currentVersionCode, currentVersionName,
                latestVersionName, latestVersionCode, downloadUrl, updateLog, createTime);
    }

    @Override
    public String toString() {
        return "VersionCheckResult{" +
                "hasNewVersion=" + hasNewVersion +
                ", latestVersionName='" + latestVersionName + '\'' +
                ", currentVersionName='" + currentVersionName + '\'' +
                ", message='" + message + '\'' +
                '}';
    }
}
