package com.hj.manage.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hj.manage.domain.Tool;
import com.hj.manage.repository.ToolRepository;
import com.hj.manage.utils.QRCodeUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/qr")
public class QRCodeController {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Autowired
    private ToolRepository toolRepository;

    @GetMapping("/generate-qrcode")
    public ResponseEntity<byte[]> generateQRCode(
            @RequestParam String toolCode,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String model,
            @RequestParam(required = false) String specification,
            @RequestParam(required = false) String supplier,
            @RequestParam(required = false) String purchaseDate,
            @RequestParam(required = false) String price,
            @RequestParam(required = false) String status) {
        try {
            // 创建包含所有Tool信息的Map，使用LinkedHashMap保持顺序
            Map<String, String> toolInfo = new LinkedHashMap<>();
            toolInfo.put("编号", toolCode);
            if (name != null) toolInfo.put("名称", name);
            if (model != null) toolInfo.put("型号", model);
            if (specification != null) toolInfo.put("图号", specification);
            if (supplier != null) toolInfo.put("供应商", supplier);
            if (purchaseDate != null) toolInfo.put("采购日期", purchaseDate);
            if (price != null) toolInfo.put("价格", price);
            if (status != null) toolInfo.put("状态", status);

            // 转换为JSON字符串
            String jsonContent = objectMapper.writeValueAsString(toolInfo);

            // 生成二维码
            byte[] qrCodeBytes = QRCodeUtil.generateQRCode(jsonContent);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.IMAGE_PNG);
            headers.setContentDispositionFormData("attachment", toolCode + "_qrcode.png");
            return new ResponseEntity<>(qrCodeBytes, headers, HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/tool/{id}")
    public ResponseEntity<byte[]> generateToolQRCode(@PathVariable Integer id) {
        try {
            Optional<Tool> optionalTool = toolRepository.findById(id);
            if (!optionalTool.isPresent()) {
                return new ResponseEntity<>(HttpStatus.NOT_FOUND);
            }

            Tool tool = optionalTool.get();

            // 创建包含工具信息的Map，使用LinkedHashMap保持顺序
            Map<String, String> toolInfo = new LinkedHashMap<>();
            toolInfo.put("编号", tool.getToolCode());
            toolInfo.put("名称", tool.getName());

            if (tool.getModel() != null) {
                toolInfo.put("型号", tool.getModel());
            }

            if (tool.getSpecification() != null) {
                toolInfo.put("图号", tool.getSpecification());
            }

            if (tool.getSupplier() != null) {
                toolInfo.put("供应商", tool.getSupplier());
            }

            if (tool.getPurchaseDate() != null) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                toolInfo.put("采购日期", sdf.format(tool.getPurchaseDate()));
            }

            if (tool.getPrice() != null) {
                toolInfo.put("价格", tool.getPrice().toString());
            }

            // 转换为JSON字符串
            String jsonContent = objectMapper.writeValueAsString(toolInfo);

            // 生成二维码
            byte[] qrCodeBytes = QRCodeUtil.generateQRCode(jsonContent);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.IMAGE_PNG);
            headers.setContentDispositionFormData("attachment", tool.getToolCode() + "_qrcode.png");
            return new ResponseEntity<>(qrCodeBytes, headers, HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/tool/code/{toolCode}")
    public ResponseEntity<byte[]> generateToolQRCodeByCode(@PathVariable String toolCode) {
        try {
            Optional<Tool> optionalTool = toolRepository.findByToolCode(toolCode);
            if (!optionalTool.isPresent()) {
                return new ResponseEntity<>(HttpStatus.NOT_FOUND);
            }

            Tool tool = optionalTool.get();

            // 创建包含工具信息的Map，使用LinkedHashMap保持顺序
            Map<String, String> toolInfo = new LinkedHashMap<>();
            toolInfo.put("编号", tool.getToolCode());
            toolInfo.put("名称", tool.getName());

            if (tool.getModel() != null) {
                toolInfo.put("型号", tool.getModel());
            }

            if (tool.getSpecification() != null) {
                toolInfo.put("图号", tool.getSpecification());
            }

            if (tool.getSupplier() != null) {
                toolInfo.put("供应商", tool.getSupplier());
            }

            if (tool.getPurchaseDate() != null) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                toolInfo.put("采购日期", sdf.format(tool.getPurchaseDate()));
            }

            if (tool.getPrice() != null) {
                toolInfo.put("价格", tool.getPrice().toString());
            }

            // 转换为JSON字符串
            String jsonContent = objectMapper.writeValueAsString(toolInfo);

            // 生成二维码
            byte[] qrCodeBytes = QRCodeUtil.generateQRCode(jsonContent);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.IMAGE_PNG);
            headers.setContentDispositionFormData("attachment", tool.getToolCode() + "_qrcode.png");
            return new ResponseEntity<>(qrCodeBytes, headers, HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/tool/info/{toolCode}")
    public ResponseEntity<byte[]> generateToolInfoQRCode(@PathVariable String toolCode) {
        try {
            Optional<Tool> optionalTool = toolRepository.findByToolCode(toolCode);
            if (!optionalTool.isPresent()) {
                return new ResponseEntity<>(HttpStatus.NOT_FOUND);
            }

            Tool tool = optionalTool.get();

            // 只生成包含工具编号的二维码
            String content = tool.getToolCode();

            // 生成二维码
            byte[] qrCodeBytes = QRCodeUtil.generateQRCode(content);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.IMAGE_PNG);
            headers.setContentDispositionFormData("attachment", tool.getToolCode() + "_qrcode.png");
            return new ResponseEntity<>(qrCodeBytes, headers, HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}