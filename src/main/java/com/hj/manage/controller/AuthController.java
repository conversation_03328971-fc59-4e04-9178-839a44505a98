package com.hj.manage.controller;

import com.hj.manage.domain.User;
import com.hj.manage.dto.ApiResponse;
import com.hj.manage.dto.user.*;
import com.hj.manage.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/auth")
@Validated
public class AuthController {

    @Autowired
    private UserService userService;

    /**
     * 用户登录
     */
    @PostMapping("/login")
    public ResponseEntity<ApiResponse<UserResponse>> login(
            @RequestParam("username") String username,
            @RequestParam("password") String password) {
        try {
            User user = userService.login(username, password);
            UserResponse userResponse = UserResponse.fromUser(user);
            return ResponseEntity.ok(ApiResponse.success(userResponse, "登录成功"));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.badRequest(e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ApiResponse.serverError("登录失败"));
        }
    }

    /**
     * 添加用户
     */
    @PostMapping("/add")
    public ResponseEntity<ApiResponse<UserResponse>> addUser(@Valid @RequestBody CreateUserRequest request) {
        try {
            User user = new User();
            user.setUsername(request.getUsername());
            user.setPassword(request.getPassword());
            user.setRole(request.getRole());
            user.setPhone(request.getPhone());

            User savedUser = userService.add(user);
            UserResponse userResponse = UserResponse.fromUser(savedUser);
            return ResponseEntity.ok(ApiResponse.success(userResponse, "用户创建成功"));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.badRequest(e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ApiResponse.serverError("用户创建失败"));
        }
    }

    /**
     * 注册新用户
     */
    @PostMapping("/register")
    public ResponseEntity<ApiResponse<UserResponse>> registerUser(@Valid @RequestBody CreateUserRequest request) {
        try {
            User user = userService.registerUser(request.getUsername(), request.getPassword(), request.getPhone());
            UserResponse userResponse = UserResponse.fromUser(user);
            return ResponseEntity.ok(ApiResponse.success(userResponse, "注册成功"));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.badRequest(e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ApiResponse.serverError("注册失败"));
        }
    }

    /**
     * 重置密码
     */
    @PostMapping("/reset")
    public ResponseEntity<ApiResponse<UserResponse>> resetPassword(
            @RequestParam("username") String username,
            @RequestParam("phone") String phone,
            @RequestParam("newPassword") String newPassword) {
        try {
            User updatedUser = userService.resetPassword(username, phone, newPassword);
            UserResponse userResponse = UserResponse.fromUser(updatedUser);
            return ResponseEntity.ok(ApiResponse.success(userResponse, "密码重置成功"));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(ApiResponse.badRequest(e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ApiResponse.serverError("密码重置失败"));
        }
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/{userId}")
    public ResponseEntity<ApiResponse<UserResponse>> updateUserInfo(
            @PathVariable("userId") Integer userId,
            @Valid @RequestBody UpdateUserRequest request) {
        try {
            User updatedUser = new User();
            updatedUser.setUsername(request.getUsername());
            updatedUser.setPassword(request.getPassword());
            updatedUser.setRole(request.getRole());
            updatedUser.setPhone(request.getPhone());

            User updated = userService.updateUserInfo(userId, updatedUser);
            UserResponse userResponse = UserResponse.fromUser(updated);
            return ResponseEntity.ok(ApiResponse.success(userResponse, "用户信息更新成功"));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(ApiResponse.badRequest(e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ApiResponse.serverError("用户信息更新失败"));
        }
    }

    /**
     * 获取所有用户
     */
    @GetMapping("/all")
    public ResponseEntity<ApiResponse<List<UserResponse>>> getAllUsers() {
        try {
            List<User> users = userService.getAllUsers();
            List<UserResponse> userResponses = users.stream()
                    .map(UserResponse::fromUser)
                    .collect(Collectors.toList());
            return ResponseEntity.ok(ApiResponse.success(userResponses));
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ApiResponse.serverError("获取用户列表失败"));
        }
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/{userId}")
    public ResponseEntity<ApiResponse<Void>> deleteUser(@PathVariable("userId") Integer userId) {
        try {
            userService.deleteUser(userId);
            return ResponseEntity.ok(ApiResponse.success("用户删除成功"));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(ApiResponse.badRequest(e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ApiResponse.serverError("用户删除失败"));
        }
    }
}