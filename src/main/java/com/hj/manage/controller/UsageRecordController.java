package com.hj.manage.controller;

import com.hj.manage.domain.UsageRecord;
import com.hj.manage.service.UsageRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/usage")
public class UsageRecordController {

    private final UsageRecordService usageRecordService;

    @Autowired
    public UsageRecordController(UsageRecordService usageRecordService) {
        this.usageRecordService = usageRecordService;
    }

    //  注册使用记录
    @PostMapping("/register")
    public ResponseEntity<?> registerUsage(
            @RequestParam String toolCode,
            @RequestParam String machineCode,
            @RequestParam String taskCode,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {

        String result = usageRecordService.registerUsage(toolCode, machineCode, taskCode, startTime, endTime);
        
        // 根据返回结果处理不同情况
        if ("success".equals(result)) {
            return ResponseEntity.ok().build();
        } else {
            Map<String, String> response = new HashMap<>();
            response.put("error", result);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }
    }

    //  获取使用次数
    @GetMapping("/count")
    public long getUsageCount(@RequestParam String toolCode) {
        return usageRecordService.getUsageCountByToolCode(toolCode);
    }

    // 获取工具的所有使用记录
    @GetMapping("/records")
    public ResponseEntity<List<UsageRecord>> getUsageRecords(@RequestParam String toolCode) {
        List<UsageRecord> records = usageRecordService.getUsageRecordsByToolCode(toolCode);
        return ResponseEntity.ok(records);
    }

    //  获取总使用时间
    @GetMapping("/time")
    public ResponseEntity<Map<String, Object>> getTotalUsageTime(@RequestParam String toolCode) {
        long totalUsageTimeInMinutes = usageRecordService.getTotalUsageTimeInMinutes(toolCode);

        Map<String, Object> response = new HashMap<>();
        response.put("toolCode", toolCode);
        response.put("totalUsageTimeInMinutes", totalUsageTimeInMinutes);

        return ResponseEntity.ok(response);
    }
}