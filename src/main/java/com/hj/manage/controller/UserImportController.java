package com.hj.manage.controller;

import com.hj.manage.service.UserImportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

/**
 * 用户导入控制器
 */
@RestController
@RequestMapping("/api/user/import")
public class UserImportController {

    private final UserImportService userImportService;

    @Autowired
    public UserImportController(UserImportService userImportService) {
        this.userImportService = userImportService;
    }

    /**
     * 处理用户Excel导入请求
     * @param file Excel文件
     * @return 导入结果
     */
    @PostMapping
    public ResponseEntity<Map<String, Object>> importUsers(@RequestParam("file") MultipartFile file) {
        // 文件类型检查
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null ||
                !(originalFilename.endsWith(".xlsx") || originalFilename.endsWith(".xls"))) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "请上传Excel文件(.xlsx或.xls格式)");
            return ResponseEntity
                    .status(HttpStatus.BAD_REQUEST)
                    .body(errorResponse);
        }

        // 调用服务处理导入
        Map<String, Object> result = userImportService.importUsers(file);

        return ResponseEntity.ok(result);
    }
} 