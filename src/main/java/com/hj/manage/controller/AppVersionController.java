package com.hj.manage.controller;

import com.hj.manage.domain.AppVersion;
import com.hj.manage.dto.ApiResponse;
import com.hj.manage.dto.VersionCheckResult;
import com.hj.manage.dto.version.CreateVersionRequest;
import com.hj.manage.service.AppVersionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

/**
 * 应用版本管理控制器
 * 提供版本管理相关的API接口
 */
@RestController
@RequestMapping("/api/version")
@CrossOrigin(origins = "*") // 允许跨域访问
@Validated
public class AppVersionController {

    private static final Logger logger = Logger.getLogger(AppVersionController.class.getName());

    @Autowired
    private AppVersionService appVersionService;

    /**
     * 检查版本更新（通过版本代码）
     * @param currentVersionCode 当前版本代码
     * @return 版本检查结果
     */
    @GetMapping("/check")
    public ResponseEntity<VersionCheckResult> checkVersion(
            @RequestParam("currentVersionCode") Integer currentVersionCode) {
        try {
            logger.info("版本检查请求，当前版本代码: " + currentVersionCode);
            VersionCheckResult result = appVersionService.checkForUpdate(currentVersionCode);
            return ResponseEntity.ok(result);
        } catch (IllegalArgumentException e) {
            logger.warning("版本检查参数错误: " + e.getMessage());
            return ResponseEntity.badRequest().body(null);
        } catch (Exception e) {
            logger.severe("版本检查失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(null);
        }
    }

    /**
     * 检查版本更新（通过版本号）
     * @param currentVersionName 当前版本号
     * @return 版本检查结果
     */
    @GetMapping("/check-by-name")
    public ResponseEntity<VersionCheckResult> checkVersionByName(
            @RequestParam("currentVersionName") String currentVersionName) {
        try {
            logger.info("版本检查请求，当前版本号: " + currentVersionName);
            VersionCheckResult result = appVersionService.checkForUpdate(currentVersionName);
            return ResponseEntity.ok(result);
        } catch (IllegalArgumentException e) {
            logger.warning("版本检查参数错误: " + e.getMessage());
            return ResponseEntity.badRequest().body(null);
        } catch (Exception e) {
            logger.severe("版本检查失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(null);
        }
    }

    /**
     * 获取最新版本
     * @return 最新版本
     */
    @GetMapping("/latest")
    public ResponseEntity<AppVersion> getLatestVersion() {
        try {
            AppVersion latestVersion = appVersionService.getLatestVersion();
            if (latestVersion != null) {
                return ResponseEntity.ok(latestVersion);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            logger.severe("获取最新版本失败: " + e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取所有版本列表（管理端使用）
     * @return 版本列表
     */
    @GetMapping("/all")
    public ResponseEntity<ApiResponse<List<AppVersion>>> getAllVersions() {
        try {
            List<AppVersion> versions = appVersionService.getAllVersions();
            return ResponseEntity.ok(ApiResponse.success(versions));
        } catch (Exception e) {
            logger.severe("获取版本列表失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(ApiResponse.serverError("获取版本列表失败"));
        }
    }

    /**
     * 根据ID获取版本详情
     * @param id 版本ID
     * @return 版本详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<AppVersion> getVersionById(@PathVariable Integer id) {
        try {
            AppVersion version = appVersionService.getVersionById(id);
            return ResponseEntity.ok(version);
        } catch (RuntimeException e) {
            logger.warning("获取版本详情失败: " + e.getMessage());
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            logger.severe("获取版本详情失败: " + e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 创建新版本（管理端使用）
     * @param request 版本创建请求
     * @return 创建的版本
     */
    @PostMapping("/create")
    public ResponseEntity<ApiResponse<AppVersion>> createVersion(@Valid @RequestBody CreateVersionRequest request) {
        try {
            logger.info("创建新版本请求: " + request.getVersionName());

            // 创建版本对象
            AppVersion appVersion = new AppVersion();
            appVersion.setVersionName(request.getVersionName().trim());
            appVersion.setDownloadUrl(request.getDownloadUrl());
            appVersion.setUpdateLog(request.getUpdateLog());

            // 创建版本
            AppVersion createdVersion = appVersionService.createNewVersion(appVersion);

            logger.info("版本创建成功: " + createdVersion.getVersionName());
            return ResponseEntity.ok(ApiResponse.success(createdVersion, "版本创建成功"));

        } catch (IllegalArgumentException e) {
            logger.warning("创建版本参数错误: " + e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.badRequest(e.getMessage()));
        } catch (Exception e) {
            logger.severe("创建版本失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(ApiResponse.serverError("创建版本失败"));
        }
    }





    /**
     * 删除版本（管理端使用）
     * @param id 版本ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteVersion(@PathVariable Integer id) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            appVersionService.deleteVersion(id);
            response.put("success", true);
            response.put("message", "版本删除成功");
            return ResponseEntity.ok(response);
        } catch (RuntimeException e) {
            logger.warning("删除版本失败: " + e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            logger.severe("删除版本失败: " + e.getMessage());
            response.put("success", false);
            response.put("message", "删除版本失败");
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 获取下一个建议的版本号
     * @return 建议的版本号
     */
    @GetMapping("/next-suggested")
    public ResponseEntity<Map<String, Object>> getNextSuggestedVersion() {
        try {
            String nextVersionName = appVersionService.getNextSuggestedVersionName();
            Integer nextVersionCode = appVersionService.getNextSuggestedVersionCode();
            
            Map<String, Object> result = new HashMap<>();
            result.put("versionName", nextVersionName);
            result.put("versionCode", nextVersionCode);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.severe("获取建议版本号失败: " + e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取版本统计信息
     * @return 统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<AppVersionService.VersionStatistics> getVersionStatistics() {
        try {
            AppVersionService.VersionStatistics statistics = appVersionService.getVersionStatistics();
            return ResponseEntity.ok(statistics);
        } catch (Exception e) {
            logger.severe("获取版本统计失败: " + e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }


}
