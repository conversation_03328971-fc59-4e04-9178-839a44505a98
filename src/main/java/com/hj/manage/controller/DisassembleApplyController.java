package com.hj.manage.controller;

import com.hj.manage.domain.DisassembleApply;
import com.hj.manage.service.DisassembleApplyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/disassemble-apply")
public class DisassembleApplyController {

    @Autowired
    private DisassembleApplyService disassembleApplyService;

    //  提交拆解申请
    @PostMapping("/submit")
    public ResponseEntity<?> submitDisassembleApplication(
            @RequestParam("toolCode") String toolCode,
            @RequestParam("applyTime") LocalDateTime applyTime,
            @RequestParam("reason") String reason,
            @RequestParam("operator") String operator,
            @RequestParam("file") MultipartFile file) throws IOException {

        String imageUrl = saveFileAndGetUrl(file);

        return ResponseEntity.ok(disassembleApplyService.submitDisassembleApplication(toolCode, applyTime, reason, imageUrl, operator));
    }

    // 辅助方法：保存文件并返回其URL
    private String saveFileAndGetUrl(MultipartFile file) throws IOException {
        // 文件存储逻辑，这里仅作示例
        String fileName = StringUtils.cleanPath(file.getOriginalFilename());
        Path fileStorageLocation = Paths.get("uploads").toAbsolutePath().normalize();
        Files.createDirectories(fileStorageLocation);

        Path targetLocation = fileStorageLocation.resolve(fileName);
        Files.copy(file.getInputStream(), targetLocation, StandardCopyOption.REPLACE_EXISTING);

        // 返回可访问的完整URL（根据你的服务器IP和端口）
        String serverBaseUrl = "http://*************:8080"; // ← 替换为你实际部署的地址
        return serverBaseUrl + "/uploads/" + fileName;
    }

    // 同意拆解申请 - 只更新申请状态，不更新工具状态
    @PostMapping("/approve")
    public DisassembleApply approveDisassembleApplication(
            @RequestParam Integer applyId) {
        return disassembleApplyService.approveDisassembleApplication(applyId);
    }

    // 拒绝拆解申请
    @PostMapping("/reject")
    public String rejectDisassembleApplication(@RequestParam Integer applyId) {
        // 调用服务层方法更新状态
        disassembleApplyService.rejectDisassembleApplication(applyId);

        // 返回提示信息
        return "您提交的拆解申请已被退回";
    }

    // 获取所有待审批的拆解申请
    @GetMapping("/pending")
    public List<DisassembleApply> getAllPendingDisassembleApplications() {
        return disassembleApplyService.getAllPendingDisassembleApplications();
    }

    // 获取所有拆解申请记录（包括所有状态）
    @GetMapping("/all")
    public List<DisassembleApply> getAllDisassembleApplications() {
        return disassembleApplyService.getAllDisassembleApplications();
    }
}