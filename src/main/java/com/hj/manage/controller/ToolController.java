package com.hj.manage.controller;

import com.hj.manage.domain.Tool;
import com.hj.manage.service.ToolService;
import com.hj.manage.service.StatusChangeRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.logging.Logger;

@RestController
@RequestMapping("/tools")
public class ToolController {

    private static final Logger logger = Logger.getLogger(ToolController.class.getName());

    @Autowired
    private ToolService toolService;

    @Autowired
    private StatusChangeRecordService statusChangeRecordService;

    // 获取所有工具 - 添加自动更新状态
    @GetMapping("/all")
    public List<Tool> getAllTools() {
        // 在返回所有工具前先检查并更新状态
        toolService.updateToolStatusBasedOnUsageRecords();
        return toolService.getAllTools();
    }

    //  添加工具
    @PostMapping("/add")
    public Tool addTool(@RequestParam String toolCode,
                        @RequestParam String name,
                        @RequestParam(required = false) String model,
                        @RequestParam(required = false) String specification,
                        @RequestParam(required = false) String supplier,
                        @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date purchaseDate,
                        @RequestParam BigDecimal price) {
        return toolService.addTool(toolCode, name, model, specification, supplier, purchaseDate, price);
    }

    // 更新工具状态
    @PutMapping("/update")
    public Tool updateToolStatus(@RequestParam String toolCode,
                                 @RequestParam String newStatus) {
        // 执行状态更新
        Tool updatedTool = toolService.updateToolStatus(toolCode, newStatus);

        // 记录手动状态变更（只有通过API调用才会记录）
        statusChangeRecordService.recordManualChange(toolCode, LocalDateTime.now());

        return updatedTool;
    }

    // 根据工具编号获取工具
    @GetMapping("/{toolCode}")
    public Tool getToolByCode(@PathVariable String toolCode) {
        // 在获取单个工具前也检查更新该工具的状态
        toolService.checkAndUpdateSingleToolStatus(toolCode);
        return toolService.getToolByCode(toolCode);
    }

    //  获取拆解率
    @GetMapping("/rate")
    public Object getDisassembleRate(@RequestParam String name) {
        return toolService.calculateDisassembleRate(name);
    }

    // 手动触发状态更新的端点
    @GetMapping("/update-status")
    public ResponseEntity<String> manualStatusUpdate() {
        logger.info("手动触发工具状态更新");
        try {
            toolService.updateToolStatusBasedOnUsageRecords();
            return ResponseEntity.ok("状态更新成功");
        } catch (Exception e) {
            logger.severe("手动更新状态失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body("状态更新失败: " + e.getMessage());
        }
    }

    // 定时任务：每5分钟检查并更新工具状态
    @Scheduled(fixedRate = 300000) // 300000毫秒 = 5分钟
    public void scheduledStatusUpdate() {
        logger.info("定时任务: 开始检查工具状态");
        toolService.updateToolStatusBasedOnUsageRecords();
        logger.info("定时任务: 完成工具状态检查");
    }
}