package com.hj.manage.controller;

import com.hj.manage.service.ToolImportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

@RestController
@RequestMapping("/toolImport")
public class ToolImportController {

    @Autowired
    private ToolImportService toolImportService;

    /**
     * 导入工具数据（从Excel文件）
     *
     * @param file Excel 文件
     * @return 统一响应格式 Map<String, Object>
     */
    @PostMapping("/import")
    public ResponseEntity<Map<String, Object>> importTools(@RequestParam("file") MultipartFile file) {
        Map<String, Object> result = toolImportService.importTools(file);
        return ResponseEntity.ok(result);
    }
}