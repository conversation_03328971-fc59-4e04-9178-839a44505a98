package com.hj.manage.controller;

import com.hj.manage.domain.AppVersion;
import com.hj.manage.service.AppVersionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

@Controller
@RequestMapping("/app")
public class AppDownloadController {

    @Autowired
    private AppVersionService appVersionService;

    // APP文件存放目录（绝对路径）
    private static final String APP_DIR_PATH = "D:/QyProjects/gzjiaju/app";
    private static final String APP_NAME = "工装夹具管理系统";

    /**
     * 查找app目录下的第一个APK文件
     * @return APK文件的Path，如果没找到返回null
     */
    private Path findApkFile() {
        try {
            Path appDir = Paths.get(APP_DIR_PATH);
            if (!Files.exists(appDir) || !Files.isDirectory(appDir)) {
                return null;
            }

            return Files.list(appDir)
                    .filter(path -> path.toString().toLowerCase().endsWith(".apk"))
                    .findFirst()
                    .orElse(null);
        } catch (IOException e) {
            return null;
        }
    }

    /**
     * APP下载页面
     */
    @GetMapping("/download")
    public String downloadPage(Model model) {
        // 从数据库获取最新版本信息
        AppVersion latestVersion = appVersionService.getLatestVersion();

        model.addAttribute("appName", APP_NAME);
        if (latestVersion != null) {
            model.addAttribute("appVersion", latestVersion.getVersionName());
            model.addAttribute("versionDescription", latestVersion.getUpdateLog());
            model.addAttribute("updateTime", latestVersion.getCreateTime());
        } else {
            model.addAttribute("appVersion", "1.0.0");
            model.addAttribute("versionDescription", "工装夹具管理系统");
            model.addAttribute("updateTime", null);
        }

        // 查找APK文件
        Path apkPath = findApkFile();
        model.addAttribute("fileExists", apkPath != null);

        if (apkPath != null) {
            try {
                // 获取文件大小
                long fileSizeBytes = Files.size(apkPath);
                String fileSize = formatFileSize(fileSizeBytes);
                model.addAttribute("actualSize", fileSize);

                // 获取文件修改时间
                String lastModified = Files.getLastModifiedTime(apkPath).toString();
                model.addAttribute("lastModified", lastModified);
            } catch (IOException e) {
                model.addAttribute("actualSize", "未知");
                model.addAttribute("lastModified", "未知");
            }
        }

        return "app-download";
    }

    /**
     * 直接下载APK文件
     */
    @GetMapping("/download/apk")
    @ResponseBody
    public ResponseEntity<Resource> downloadApk() {
        try {
            Path apkPath = findApkFile();

            if (apkPath == null) {
                return ResponseEntity.notFound().build();
            }

            Resource resource = new FileSystemResource(apkPath.toFile());

            // 使用实际的文件名
            String fileName = apkPath.getFileName().toString();

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + "\"")
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(resource);

        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取APP信息API
     */
    @GetMapping("/info")
    @ResponseBody
    public ResponseEntity<?> getAppInfo() {
        // 从数据库获取当前版本信息
        AppVersion currentVersion = appVersionService.getLatestVersion();
        String versionName = currentVersion != null ? currentVersion.getVersionName() : "1.0.0";

        Path apkPath = findApkFile();

        if (apkPath == null) {
            return ResponseEntity.ok().body(new AppInfo(APP_NAME, versionName, "0MB", false, "APK文件不存在"));
        }

        try {
            long fileSizeBytes = Files.size(apkPath);
            String fileSize = formatFileSize(fileSizeBytes);
            String lastModified = Files.getLastModifiedTime(apkPath).toString();

            return ResponseEntity.ok().body(new AppInfo(APP_NAME, versionName, fileSize, true, lastModified));
        } catch (IOException e) {
            return ResponseEntity.ok().body(new AppInfo(APP_NAME, versionName, "未知", false, "读取文件信息失败"));
        }
    }

    /**
     * 格式化文件大小
     */
    private String formatFileSize(long bytes) {
        if (bytes < 1024) return bytes + " B";
        if (bytes < 1024 * 1024) return String.format("%.1f KB", bytes / 1024.0);
        if (bytes < 1024 * 1024 * 1024) return String.format("%.1f MB", bytes / (1024.0 * 1024));
        return String.format("%.1f GB", bytes / (1024.0 * 1024 * 1024));
    }

    /**
     * APP信息类
     */
    public static class AppInfo {
        private String name;
        private String version;
        private String size;
        private boolean available;
        private String lastModified;

        public AppInfo(String name, String version, String size, boolean available, String lastModified) {
            this.name = name;
            this.version = version;
            this.size = size;
            this.available = available;
            this.lastModified = lastModified;
        }

        // Getters
        public String getName() { return name; }
        public String getVersion() { return version; }
        public String getSize() { return size; }
        public boolean isAvailable() { return available; }
        public String getLastModified() { return lastModified; }
    }
}
