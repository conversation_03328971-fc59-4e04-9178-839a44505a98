package com.hj.manage.controller;

import com.hj.manage.domain.DisassembleRecord;
import com.hj.manage.service.DisassembleRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/disassemble-record")
public class DisassembleRecordController {

    @Autowired
    private DisassembleRecordService disassembleRecordService;

    // 创建拆解记录
    @PostMapping("/create")
    public ResponseEntity<DisassembleRecord> createDisassembleRecord(
            @RequestParam("toolCode") String toolCode,
            @RequestParam("name") String name,
            @RequestParam("disassembleInfo") String disassembleInfo,
            @RequestParam("disassembleTime") @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm") LocalDateTime disassembleTime,
            @RequestParam("operator") String operator,
            @RequestParam(value = "applyId", required = false) Integer applyId) {

        DisassembleRecord record = disassembleRecordService.createDisassembleRecord(
                toolCode, name, disassembleInfo, disassembleTime, operator, applyId);

        return ResponseEntity.ok(record);
    }

    // 获取所有拆解记录
    @GetMapping("/all")
    public ResponseEntity<List<DisassembleRecord>> getAllDisassembleRecords() {
        List<DisassembleRecord> records = disassembleRecordService.getAllDisassembleRecords();
        return ResponseEntity.ok(records);
    }
}