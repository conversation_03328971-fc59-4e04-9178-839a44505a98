@echo off
setlocal

:: =================================================================
:: Configuration
:: This section defines the core settings for the service.
:: =================================================================

:: A unique name for the application window. This is used to check if the service is already running.
set "SERVICE_WINDOW_TITLE=GZ_FixtureManage_Service"

:: The name of the JAR file to run.
set "JAR_NAME=manage-0.0.1-SNAPSHOT.jar"


:: =================================================================
:: Script Logic
:: Do not modify below this line.
:: =================================================================

:: Change the current directory to the script's own directory.
:: This makes the script portable.
cd /d "%~dp0"

:: Check if an instance of the service is already running by looking for the process.
echo [INFO] Checking for an already running service instance...
tasklist /NH /FI "IMAGENAME eq java.exe" /FI "WINDOWTITLE eq %SERVICE_WINDOW_TITLE%" | find "java.exe" > nul
if %errorlevel% equ 0 (
    echo [WARNING] An instance of the service appears to be running.
    echo Please close any existing service windows before starting a new one.
    pause
    goto :eof
)

:: Check if the specified JAR file exists in the current directory.
echo [INFO] Searching for JAR file: %JAR_NAME%...
if not exist "%JAR_NAME%" (
    echo [ERROR] The JAR file '%JAR_NAME%' was not found in this directory.
    echo Please make sure this script is in the same folder as the JAR file.
    pause
    goto :eof
)

echo [INFO] JAR file found. Starting the application...
echo.
echo ===============================================================
echo  This window is the service console. It will show live logs.
echo.
echo  DO NOT CLOSE THIS WINDOW, as doing so will stop the service.
echo ===============================================================
echo.

:: Set the window title for this new instance and start the Java application.
title %SERVICE_WINDOW_TITLE%
java -jar "%JAR_NAME%"

echo.
echo The application has stopped. Press any key to close this window.
pause 